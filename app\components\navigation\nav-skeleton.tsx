import { Skeleton } from "@/app/components/ui/skeleton";
import { cn } from "@/lib/utils/cn";

interface NavSkeletonProps {
  className?: string;
  itemCount?: number;
  variant?: "mobile" | "desktop";
}

export function NavSkeleton({ 
  className, 
  itemCount = 4, 
  variant = "desktop" 
}: NavSkeletonProps) {
  if (variant === "mobile") {
    return (
      <div className={cn("md:hidden", className)}>
        {/* Mobile Menu Button Skeleton */}
        <Skeleton className="h-11 w-11 min-h-[44px] min-w-[44px] rounded-xl tablet:min-h-14 tablet:min-w-14" variant="violet" />
      </div>
    );
  }

  return (
    <nav className={cn("hidden md:flex items-center space-x-2", className)} role="navigation">
      {Array.from({ length: itemCount }).map((_, index) => (
        <Skeleton
          key={index}
          className="h-11 w-full max-w-24 rounded-xl tablet:text-sm"
          variant="violet"
          style={{
            animationDelay: `${index * 100}ms`
          }}
        />
      ))}
    </nav>
  );
}

// Mobile menu panel skeleton
export function MobileMenuSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn(
      "fixed top-0 right-0 h-full w-80 max-w-[85vw] bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl shadow-2xl z-40 border-l border-violet-100 dark:border-violet-800 transition-all duration-300 ease-in-out transform",
      className
    )}>
      {/* Menu Header Skeleton */}
      <div className="p-6 border-b border-violet-100 dark:border-violet-800 bg-gradient-to-r from-violet-50 to-purple-50 dark:from-violet-950/50 dark:to-purple-950/50 flex items-center gap-4">
        <Skeleton className="w-12 h-12 rounded-xl" variant="violet" />
        <div className="flex flex-col gap-2 flex-1">
          <Skeleton className="h-5 w-40" variant="violet" />
          <Skeleton className="h-3 w-32" variant="violet" />
        </div>
      </div>

      {/* Menu Items Skeleton */}
      <div className="flex-1 p-4 space-y-3">
        {Array.from({ length: 6 }).map((_, index) => (
          <div key={index} className="flex items-center min-h-[50px] px-4 py-3 bg-white/80 dark:bg-gray-800/80 rounded-lg">
            <Skeleton className="w-6 h-6 mr-4 rounded-full" variant="violet" />
            <Skeleton className="h-4 w-3/4" variant="violet" />
          </div>
        ))}
      </div>

      {/* Profile Section Skeleton */}
      <div className="p-4 border-t border-violet-100 dark:border-violet-800">
        <div className="flex items-center gap-3 mb-4">
          <Skeleton className="w-10 h-10 rounded-full tablet:min-h-10 tablet:min-w-10" variant="violet" />
          <div className="flex-1">
            <Skeleton className="h-4 w-full max-w-32 mb-2" variant="violet" />
            <Skeleton className="h-3 w-full max-w-24" variant="violet" />
          </div>
        </div>
        <div className="flex justify-between">
          <Skeleton className="h-11 w-11 rounded-lg" variant="violet" />
          <Skeleton className="h-11 w-11 rounded-lg" variant="violet" />
          <Skeleton className="h-11 w-11 rounded-lg" variant="violet" />
        </div>
      </div>
    </div>
  );
}

// User profile skeleton
export function UserProfileSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn("hidden md:flex items-center gap-3", className)}>
      <div className="flex items-center gap-3 min-h-[44px] px-4 py-2 rounded-xl bg-white/80 border border-violet-100 shadow-sm">
        {/* Avatar Skeleton */}
        <Skeleton className="w-10 h-10 rounded-full" variant="violet" />
        
        {/* User Info Skeleton */}
        <div className="flex flex-col gap-1">
          <Skeleton className="h-4 w-full max-w-20" variant="violet" />
          <Skeleton className="h-3 w-full max-w-24" variant="violet" />
        </div>
        
        {/* Dropdown Arrow Skeleton */}
        <Skeleton className="w-4 h-4 animate-pulse" />
      </div>
    </div>
  );
}

// Combined navigation skeleton for loading states
export function NavigationSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn("flex items-center justify-between", className)}>
      {/* Desktop Navigation Skeleton */}
      <NavSkeleton variant="desktop" />
      
      {/* Mobile Navigation Skeleton */}
      <NavSkeleton variant="mobile" />
      
      {/* User Profile Skeleton */}
      <UserProfileSkeleton />
    </div>
  );
}
