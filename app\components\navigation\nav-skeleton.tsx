import { Skeleton } from "@/app/components/ui/skeleton";
import { cn } from "@/lib/utils/cn";

interface NavSkeletonProps {
  className?: string;
  itemCount?: number;
  variant?: "mobile" | "desktop";
}

export function NavSkeleton({ 
  className, 
  itemCount = 4, 
  variant = "desktop" 
}: NavSkeletonProps) {
  if (variant === "mobile") {
    return (
      <div className={cn("md:hidden", className)}>
        {/* Mobile Menu Button Skeleton */}
        <Skeleton className="h-11 w-11 rounded-xl animate-pulse" />
      </div>
    );
  }

  return (
    <nav className={cn("hidden md:flex items-center space-x-2", className)} role="navigation">
      {Array.from({ length: itemCount }).map((_, index) => (
        <Skeleton 
          key={index}
          className="h-11 w-24 rounded-xl animate-pulse"
          style={{
            animationDelay: `${index * 100}ms`
          }}
        />
      ))}
    </nav>
  );
}

// Mobile menu panel skeleton
export function MobileMenuSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn(
      "fixed top-0 right-0 h-full w-80 max-w-[85vw] bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl shadow-2xl z-40 border-l border-violet-100 dark:border-violet-800",
      className
    )}>
      {/* Menu Header Skeleton */}
      <div className="p-6 border-b border-violet-100 dark:border-violet-800 bg-gradient-to-r from-violet-50 to-purple-50 dark:from-violet-950/50 dark:to-purple-950/50">
        <Skeleton className="h-6 w-32 mb-2 animate-pulse" />
        <Skeleton className="h-4 w-48 animate-pulse" />
      </div>

      {/* Menu Items Skeleton */}
      <div className="flex-1 p-4 space-y-2">
        {Array.from({ length: 4 }).map((_, index) => (
          <div key={index} className="flex items-center min-h-[44px] px-4 py-3">
            <Skeleton className="w-5 h-5 mr-3 animate-pulse" />
            <Skeleton className="h-4 flex-1 animate-pulse" />
          </div>
        ))}
      </div>

      {/* Logout Section Skeleton */}
      <div className="p-4 border-t border-violet-100 dark:border-violet-800">
        <Skeleton className="h-11 w-full rounded-xl animate-pulse" />
      </div>
    </div>
  );
}

// User profile skeleton
export function UserProfileSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn("hidden md:flex items-center gap-3", className)}>
      <div className="flex items-center gap-3 min-h-[44px] px-4 py-2 rounded-xl bg-white/80 border border-violet-100 shadow-sm">
        {/* Avatar Skeleton */}
        <Skeleton className="w-10 h-10 rounded-full animate-pulse" />
        
        {/* User Info Skeleton */}
        <div className="flex flex-col gap-1">
          <Skeleton className="h-4 w-20 animate-pulse" />
          <Skeleton className="h-3 w-24 animate-pulse" />
        </div>
        
        {/* Dropdown Arrow Skeleton */}
        <Skeleton className="w-4 h-4 animate-pulse" />
      </div>
    </div>
  );
}

// Combined navigation skeleton for loading states
export function NavigationSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn("flex items-center justify-between", className)}>
      {/* Desktop Navigation Skeleton */}
      <NavSkeleton variant="desktop" />
      
      {/* Mobile Navigation Skeleton */}
      <NavSkeleton variant="mobile" />
      
      {/* User Profile Skeleton */}
      <UserProfileSkeleton />
    </div>
  );
}
