import { Skeleton } from "@/app/components/ui/skeleton";
import { cn } from "@/lib/utils/cn";

interface NavSkeletonProps {
  className?: string;
  itemCount?: number;
  variant?: "mobile" | "desktop" | "tablet";
}

export function NavSkeleton({
  className,
  itemCount = 4,
  variant = "desktop"
}: NavSkeletonProps) {
  if (variant === "mobile") {
    return (
      <div className={cn("md:hidden", className)}>
        {/* Mobile Menu Button Skeleton - Optimized for touch */}
        <Skeleton
          variant="navigation"
          size="mobile"
          shape="rounded"
          className="w-11 h-11 shadow-sm hover:shadow-md transition-shadow duration-200"
        />
      </div>
    );
  }

  if (variant === "tablet") {
    return (
      <nav className={cn("hidden md:flex lg:hidden items-center space-x-2", className)} role="navigation">
        {Array.from({ length: itemCount }).map((_, index) => (
          <Skeleton
            key={index}
            variant="navigation"
            size="tablet"
            shape="rounded"
            className="w-20 h-10 shadow-sm"
            style={{
              animationDelay: `${index * 80}ms`
            }}
          />
        ))}
      </nav>
    );
  }

  return (
    <nav className={cn("hidden lg:flex items-center space-x-2", className)} role="navigation">
      {Array.from({ length: itemCount }).map((_, index) => (
        <Skeleton
          key={index}
          variant="navigation"
          shape="rounded"
          className="h-11 w-24 shadow-sm hover:shadow-md transition-shadow duration-200"
          style={{
            animationDelay: `${index * 100}ms`
          }}
        />
      ))}
    </nav>
  );
}

// Mobile menu panel skeleton - Optimized for mobile/tablet
export function MobileMenuSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn(
      "fixed top-0 right-0 h-full w-80 max-w-[85vw] bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl shadow-2xl z-40 border-l border-violet-100 dark:border-violet-800 transition-all duration-300 ease-out",
      className
    )}>
      {/* Menu Header Skeleton */}
      <div className="p-6 border-b border-violet-100 dark:border-violet-800 bg-gradient-to-r from-violet-50 to-purple-50 dark:from-violet-950/50 dark:to-purple-950/50">
        <Skeleton
          variant="navigation"
          shape="rounded"
          className="h-6 w-32 mb-2"
        />
        <Skeleton
          variant="mobile"
          shape="rounded"
          className="h-4 w-48"
        />
      </div>

      {/* Menu Items Skeleton - Mobile optimized */}
      <div className="flex-1 p-4 space-y-2">
        {Array.from({ length: 4 }).map((_, index) => (
          <div
            key={index}
            className="flex items-center min-h-[44px] px-4 py-3 rounded-xl"
            style={{
              animationDelay: `${index * 50}ms`
            }}
          >
            {/* Icon skeleton */}
            <Skeleton
              variant="navigation"
              shape="circle"
              className="w-5 h-5 mr-3 flex-shrink-0"
            />
            {/* Text skeleton */}
            <Skeleton
              variant="mobile"
              shape="rounded"
              className="h-4 flex-1 max-w-[120px]"
            />
            {/* Active indicator skeleton (for first item) */}
            {index === 0 && (
              <Skeleton
                variant="violet"
                shape="circle"
                className="w-2 h-2 ml-2 animate-pulse"
              />
            )}
          </div>
        ))}
      </div>

      {/* Logout Section Skeleton */}
      <div className="p-4 border-t border-violet-100 dark:border-violet-800 bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-950/30 dark:to-pink-950/30">
        <Skeleton
          variant="navigation"
          size="mobile"
          shape="rounded"
          className="w-full h-11 shadow-lg"
        />
      </div>
    </div>
  );
}

// User profile skeleton - Responsive for desktop/tablet
export function UserProfileSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn("hidden md:flex items-center gap-3", className)}>
      <div className="flex items-center gap-3 min-h-[44px] px-4 py-2 rounded-xl bg-white/80 border border-violet-100 shadow-sm hover:shadow-md transition-shadow duration-200 dark:bg-gray-800/80 dark:border-violet-800">
        {/* Avatar Skeleton with gradient */}
        <div className="relative">
          <Skeleton
            variant="navigation"
            shape="circle"
            className="w-10 h-10 shadow-md"
          />
          {/* Online indicator skeleton */}
          <Skeleton
            variant="violet"
            shape="circle"
            className="absolute -bottom-0.5 -right-0.5 w-3 h-3 border-2 border-white dark:border-gray-800 animate-pulse"
          />
        </div>

        {/* User Info Skeleton */}
        <div className="flex flex-col gap-1 min-w-0">
          <Skeleton
            variant="navigation"
            shape="rounded"
            className="h-4 w-20"
          />
          <Skeleton
            variant="mobile"
            shape="rounded"
            className="h-3 w-24"
          />
        </div>

        {/* Dropdown Arrow Skeleton */}
        <Skeleton
          variant="violet"
          shape="rounded"
          className="w-4 h-4"
        />
      </div>
    </div>
  );
}

// Combined navigation skeleton for loading states - Responsive
export function NavigationSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn("flex items-center justify-between p-4 lg:p-6", className)}>
      {/* Desktop Navigation Skeleton */}
      <NavSkeleton variant="desktop" />

      {/* Tablet Navigation Skeleton */}
      <NavSkeleton variant="tablet" />

      {/* Mobile Navigation Skeleton */}
      <NavSkeleton variant="mobile" />

      {/* User Profile Skeleton */}
      <UserProfileSkeleton />
    </div>
  );
}

// Navigation item skeleton - Reusable component
interface NavItemSkeletonProps {
  className?: string;
  showIcon?: boolean;
  showIndicator?: boolean;
  variant?: "mobile" | "desktop" | "tablet";
}

export function NavItemSkeleton({
  className,
  showIcon = true,
  showIndicator = false,
  variant = "desktop"
}: NavItemSkeletonProps) {
  const sizeClasses = {
    mobile: "min-h-[44px] px-4 py-3",
    tablet: "min-h-[42px] px-3 py-2.5",
    desktop: "min-h-[44px] px-4 py-2.5"
  };

  const iconSizes = {
    mobile: "w-5 h-5",
    tablet: "w-4 h-4",
    desktop: "w-4 h-4"
  };

  const textWidths = {
    mobile: "w-24",
    tablet: "w-20",
    desktop: "w-24"
  };

  return (
    <div className={cn(
      "flex items-center rounded-xl",
      sizeClasses[variant],
      className
    )}>
      {showIcon && (
        <Skeleton
          variant="navigation"
          shape="circle"
          className={cn(iconSizes[variant], "mr-3 flex-shrink-0")}
        />
      )}
      <Skeleton
        variant="mobile"
        shape="rounded"
        className={cn("h-4", textWidths[variant])}
      />
      {showIndicator && (
        <Skeleton
          variant="violet"
          shape="circle"
          className="w-2 h-2 ml-2 animate-pulse"
        />
      )}
    </div>
  );
}

// User profile dropdown skeleton
export function UserProfileDropdownSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn(
      "absolute top-full right-0 mt-2 w-64 bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl rounded-xl shadow-2xl border border-violet-100 dark:border-violet-800 z-20 overflow-hidden",
      className
    )}>
      {/* User Info Header Skeleton */}
      <div className="p-4 bg-gradient-to-r from-violet-50 to-purple-50 dark:from-violet-950/50 dark:to-purple-950/50 border-b border-violet-100 dark:border-violet-800">
        <div className="flex items-center gap-3">
          <Skeleton
            variant="navigation"
            shape="circle"
            className="w-12 h-12 shadow-lg"
          />
          <div className="flex-1 min-w-0">
            <Skeleton
              variant="navigation"
              shape="rounded"
              className="h-5 w-32 mb-1"
            />
            <Skeleton
              variant="mobile"
              shape="rounded"
              className="h-4 w-40"
            />
          </div>
        </div>
      </div>

      {/* Logout Section Skeleton */}
      <div className="p-3">
        <Skeleton
          variant="navigation"
          size="mobile"
          shape="rounded"
          className="w-full h-11 shadow-lg"
        />
      </div>
    </div>
  );
}
