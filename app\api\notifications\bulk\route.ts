import { prisma } from "@/lib/config/prisma";
import { auth } from "@/auth";
import { NextResponse } from "next/server";

export async function POST(request: Request) {
  try {
    const session = await auth();
    if (!session?.user || session.user.role !== "ADMIN") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { title, message, type, userIds } = await request.json();

    // Validasi input wajib
    if (!title || !message || !type) {
      return NextResponse.json(
        { error: "<PERSON><PERSON><PERSON>, pesan, dan tipe notifikasi wajib diisi" },
        { status: 400 }
      );
    }

    let notifications = [];

    // Jika userIds tidak ada, kirim ke semua pengguna
    if (!userIds || userIds.length === 0) {
      // Dapatkan semua ID pengguna
      const users = await prisma.user.findMany({
        select: { id: true }
      });

      // Buat notifikasi untuk setiap pengguna
      notifications = await Promise.all(
        users.map(user =>
          prisma.notification.create({
            data: {
              userId: user.id,
              title,
              message,
              type,
              isRead: false
            }
          })
        )
      );
    } else {
      // Kirim hanya ke pengguna yang ditentukan
      notifications = await Promise.all(
        userIds.map((userId: string) =>
          prisma.notification.create({
            data: {
              userId,
              title,
              message,
              type,
              isRead: false
            }
          })
        )
      );
    }

    return NextResponse.json({
      success: true,
      message: `Berhasil mengirim ${notifications.length} notifikasi`,
      count: notifications.length
    });
  } catch (error) {
    console.error("[BULK_NOTIFICATION_ERROR]", error);
    return NextResponse.json(
      { error: "Gagal mengirim notifikasi massal" },
      { status: 500 }
    );
  }
} 
