import { NextResponse } from "next/server";
import { auth } from "@/auth";
import { prisma } from "@/lib/config/prisma";
import { WhatsAppService } from "@/lib/services/whatsapp";

export async function POST(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session?.user || session.user.role !== "ADMIN") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;

    // Get payment data with related information
    const payment = await prisma.payment.findUnique({
      where: { id },
      include: {
        rental: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                phone: true
              }
            },
            product: true
          }
        }
      }
    });

    if (!payment) {
      return NextResponse.json({ error: "Payment not found" }, { status: 404 });
    }

    const user = payment.rental.user;
    if (!user.phone) {
      return NextResponse.json({ error: "Customer phone number not available" }, { status: 400 });
    }

    const invoiceNumber = `INV-${payment.id.substring(0, 8)}`;
    const rental = payment.rental;
    const product = rental.product;

    // Email functionality removed - WhatsApp features simplified
    console.log(`Invoice ${invoiceNumber} for ${user.name} - Email functionality removed`);

    return NextResponse.json({
      success: false,
      error: "Email functionality has been removed. Please use other communication methods.",
      removed: true
    }, { status: 410 });

  } catch (error) {
    console.error("Error sending invoice WhatsApp notification:", error);
    return NextResponse.json(
      { error: "Failed to send invoice WhatsApp notification" },
      { status: 500 }
    );
  }
}
