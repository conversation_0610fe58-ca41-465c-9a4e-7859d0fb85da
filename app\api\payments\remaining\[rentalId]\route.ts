import { auth } from "@/auth";
import { prisma } from "@/lib/config/prisma";
import { NextResponse } from "next/server";
import MidtransService from "@/lib/services/midtrans";
import { calculateOvertimeCost } from "@/lib/utils/calculate";

export const runtime = 'nodejs';

// POST /api/payments/remaining/[rentalId] - Create remaining payment
export async function POST(
  request: Request,
  { params }: { params: Promise<{ rentalId: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { rentalId } = await params;
    
    const rental = await prisma.rental.findUnique({
      where: { id: rentalId },
      include: { 
        payment: true,
        product: true,
        user: true
      }
    });

    if (!rental) {
      return NextResponse.json({ error: "Rental tidak ditemukan" }, { status: 404 });
    }

    // Pastikan user yang login adalah pemilik rental
    if (rental.userId !== session.user.id) {
      return NextResponse.json({ error: "Tidak memiliki akses ke rental ini" }, { status: 403 });
    }

    // Cek apakah payment record ada dan deposit sudah dibayar
    if (!rental.payment) {
      return NextResponse.json({ error: "Data pembayaran tidak ditemukan" }, { status: 404 });
    }

    if (rental.payment.status !== 'DEPOSIT_PAID') {
      return NextResponse.json({ error: "Deposit harus dibayar terlebih dahulu" }, { status: 400 });
    }

    // Hitung total sisa pembayaran + overtime
    const overtime = rental.overtime || 0;
    let overtimeCost = 0;
    
    if (overtime > 0) {
      // Jika ada payment.overtime yang sudah dihitung sebelumnya, gunakan itu
      if (rental.payment.overtime) {
        overtimeCost = rental.payment.overtime;
      } else {
        // Jika tidak, hitung ulang berdasarkan overtime dan tarif produk
        overtimeCost = calculateOvertimeCost(overtime, rental.product.price);
      }
    }
    
    const remainingAmount = rental.payment.remaining + overtimeCost;

    // Buat token pembayaran Midtrans
    const payment = await MidtransService.createPayment({
      orderId: `${rental.id}_remaining_${Date.now()}`,
      amount: remainingAmount,
      email: session.user.email || "",
      name: session.user.name || "Customer",
      productName: `Sisa Pembayaran Rental Genset - ${rental.product.name}`
    });

    // Update record payment
    await prisma.payment.update({
      where: { rentalId: rental.id },
      data: {
        remaining: remainingAmount,
        overtime: overtimeCost,
        snapToken: payment.token,
        updatedAt: new Date()
      }
    });

    return NextResponse.json({ 
      success: true,
      token: payment.token,
      message: "Token pembayaran berhasil dibuat"
    });

  } catch (error) {
    console.error("[REMAINING_PAYMENT_ERROR]", error);
    return NextResponse.json(
      { error: "Gagal memproses pembayaran sisa" },
      { status: 500 }
    );
  }
}
