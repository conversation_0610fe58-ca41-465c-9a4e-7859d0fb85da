import { auth } from "@/auth";
import { prisma } from "@/lib/config/prisma";
import { redirect, notFound } from "next/navigation";
import Link from "next/link";
import { formatCurrency, formatDate } from "@/lib/utils/format";
import { revalidatePath } from "next/cache";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card";
import { Button } from "@/app/components/ui/button";
import { Textarea } from "@/app/components/ui/textarea";
import { updateProductStock } from "@/lib/actions/product";
import { notifyAllAdmins } from "@/lib/notifications";

// Pastikan halaman selalu up-to-date
export const dynamic = 'force-dynamic';

async function confirmRental(formData: FormData) {
  'use server';

  const rentalId = formData.get('rentalId') as string;
  const adminNotes = formData.get('adminNotes') as string;

  if (!rentalId) {
    throw new Error("ID Penyewaan tidak valid");
  }

  try {
    console.log(`[CONFIRM] Me<PERSON>lai proses konfirmasi rental ID: ${rentalId}`);

    // Ambil data rental terlebih dahulu untuk mendapatkan productId dan quantity
    const rental = await prisma.rental.findUnique({
      where: { id: rentalId },
      select: {
        productId: true,
        quantity: true,
        userId: true,
        status: true,
        product: {
          select: {
            id: true,
            name: true,
            stock: true
          }
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    if (!rental) {
      console.error(`[CONFIRM] Rental ${rentalId} tidak ditemukan`);
      throw new Error("Penyewaan tidak ditemukan");
    }

    // Cek status rental, jika sudah 'confirmed' maka tidak perlu konfirmasi lagi
    if (rental.status === "CONFIRMED") {
      console.log(`[DEBUG] Rental ${rentalId} sudah dalam status CONFIRMED sebelumnya`);
      redirect(`/admin/rentals/${rentalId}`);
      return;
    }

    console.log(`[DEBUG] Produk ${rental.product.name} dengan ID: ${rental.productId}`);
    console.log(`[DEBUG] Stok sebelum pengurangan: ${rental.product.stock}`);
    console.log(`[DEBUG] Jumlah yang akan dikurangi: ${rental.quantity}`);

    // Verifikasi stok cukup
    if (rental.product.stock < rental.quantity) {
      console.error(`[CONFIRM] Stok tidak mencukupi untuk rental ${rentalId}. Dibutuhkan: ${rental.quantity}, Tersedia: ${rental.product.stock}`);
      throw new Error(`Stok tidak mencukupi. Tersedia: ${rental.product.stock} unit, dibutuhkan: ${rental.quantity} unit`);
    }

    try {
      // Gunakan transaksi untuk memastikan konsistensi data
      const result = await prisma.$transaction(async (tx) => {
        // 1. Update status penyewaan menjadi confirmed
        const updatedRental = await tx.rental.update({
          where: { id: rentalId },
          data: {
            status: "CONFIRMED",
            notes: adminNotes || undefined,
          }
        });

        // 2. Kurangi stok produk langsung di database
        const updatedProduct = await tx.product.update({
          where: { id: rental.productId },
          data: {
            stock: {
              decrement: rental.quantity
            }
          }
        });

        console.log(`[DEBUG] Status rental berhasil diubah menjadi "CONFIRMED"`);
        console.log(`[DEBUG] Stok berhasil dikurangi langsung. Stok baru: ${updatedProduct.stock}`);

        return { updatedRental, updatedProduct };
      });

      // Log hasil transaksi
      console.log(`[CONFIRM RENTAL] Transaksi selesai: Rental ID=${rentalId}, Status=${result.updatedRental.status}, Stok Produk Baru=${result.updatedProduct.stock}`);

      // Juga gunakan fungsi updateProductStock untuk memastikan log dan revalidasi terpanggil
      // (ini hanya untuk log dan revalidasi, karena stok sudah diupdate dalam transaksi)
      await updateProductStock(rental.productId, -rental.quantity);

      // 3. Kirim notifikasi ke seluruh admin
      await notifyAllAdmins({
        title: "Penyewaan Dikonfirmasi",
        message: `Penyewaan ${rental.product.name} (${rental.quantity} unit) oleh ${rental.user.name || rental.user.email} telah dikonfirmasi.`,
        type: "RENTAL_CONFIRMED"
      });

      // 4. Tambah notifikasi untuk user yang menyewa
      await prisma.notification.create({
        data: {
          userId: rental.userId,
          title: "Penyewaan Dikonfirmasi",
          message: `Penyewaan Anda untuk ${rental.product.name} (${rental.quantity} unit) telah dikonfirmasi. Tim kami akan segera menghubungi Anda.`,
          type: "RENTAL_CONFIRMED",
          isRead: false
        }
      });
    } catch (txError) {
      console.error(`[CONFIRM] Error dalam transaksi database: ${txError instanceof Error ? txError.message : 'Unknown error'}`);
      throw new Error("Gagal memproses konfirmasi rental. Silakan coba lagi.");
    }

    // Revalidasi path terkait
    revalidatePath("/admin/rentals");
    revalidatePath(`/admin/rentals/${rentalId}`);
    revalidatePath("/admin/operations");
    revalidatePath("/user/operations");
    revalidatePath("/admin/products"); // Revalidasi untuk halaman produk
    revalidatePath("/user/catalog"); // Revalidasi katalog produk pengguna
    revalidatePath("/"); // Revalidasi halaman utama yang mungkin menampilkan produk

    // Redirect ke halaman detail
    console.log(`[CONFIRM] Berhasil mengkonfirmasi rental ${rentalId}, redirect ke halaman detail`);
    redirect(`/admin/rentals/${rentalId}`);
  } catch (error) {
    console.error("Error confirming rental:", error);
    throw new Error(error instanceof Error ? error.message : "Gagal mengkonfirmasi penyewaan");
  }
}

export default async function ConfirmRentalPage({ params }: { params: Promise<{ id: string }> }) {
  const session = await auth();

  // Redirect ke login jika tidak ada session atau bukan admin
  if (!session?.user || session.user.role !== "ADMIN") {
    redirect('/login');
  }

  // Ambil ID rental dari parameter URL
  const { id } = await params;

  // Ambil data rental dari database
  const rental = await prisma.rental.findUnique({
    where: { id },
    include: {
      product: true,
      user: {
        select: {
          name: true,
          email: true,
          phone: true
        }
      }
    }
  });

  // Jika rental tidak ditemukan, tampilkan halaman 404
  if (!rental) {
    notFound();
  }

  // Jika rental sudah dikonfirmasi, redirect ke halaman detail
  if (rental.status !== "PENDING") {
    redirect(`/admin/rentals/${id}`);
  }

  // Periksa apakah stok mencukupi
  const isStockSufficient = rental.product.stock >= rental.quantity;
  const stockMessage = isStockSufficient
    ? `Stok tersedia: ${rental.product.stock} unit`
    : `Stok tidak mencukupi! Tersedia: ${rental.product.stock} unit, dibutuhkan: ${rental.quantity} unit`;

  return (
    <div className="container max-w-2xl mx-auto py-6">
      <div className="flex items-center gap-3 mb-6">
        <Link href={`/admin/rentals/${id}`}>
          <Button variant="outline" className="dark:border-gray-700 dark:text-gray-200 dark:hover:bg-gray-800">Kembali</Button>
        </Link>
        <h1 className="text-2xl font-bold dark:text-white">Konfirmasi Penyewaan</h1>
      </div>

      <Card className="border dark:border-gray-800 dark:bg-gray-900">
        <CardHeader>
          <CardTitle>Konfirmasi Penyewaan #{id.slice(-6)}</CardTitle>
          <CardDescription className="dark:text-gray-400">
            Konfirmasi penyewaan akan mengubah status dari Menunggu menjadi Dikonfirmasi
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-md">
              <h3 className="font-medium mb-3 dark:text-gray-200">Detail Penyewaan</h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-gray-500 dark:text-gray-400">Produk</p>
                  <p className="font-medium dark:text-gray-200">{rental.product.name}</p>
                </div>
                <div>
                  <p className="text-gray-500 dark:text-gray-400">Pelanggan</p>
                  <p className="font-medium dark:text-gray-200">{rental.user.name}</p>
                </div>
                <div>
                  <p className="text-gray-500 dark:text-gray-400">Tanggal Mulai</p>
                  <p className="font-medium dark:text-gray-200">{formatDate(rental.startDate)}</p>
                </div>
                <div>
                  <p className="text-gray-500 dark:text-gray-400">Tanggal Selesai</p>
                  <p className="font-medium dark:text-gray-200">{formatDate(rental.endDate)}</p>
                </div>
                <div>
                  <p className="text-gray-500 dark:text-gray-400">Waktu Kedatangan</p>
                  <p className="font-medium dark:text-gray-200">{rental.arrivalTime || '-'}</p>
                </div>
                <div>
                  <p className="text-gray-500 dark:text-gray-400">Total</p>
                  <p className="font-medium dark:text-gray-200">{formatCurrency(rental.amount)}</p>
                </div>
                <div>
                  <p className="text-gray-500 dark:text-gray-400">Jumlah Unit</p>
                  <p className="font-medium dark:text-gray-200">{rental.quantity} unit</p>
                </div>
                <div>
                  <p className="text-gray-500 dark:text-gray-400">Status Stok</p>
                  <p className={`font-medium ${isStockSufficient ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                    {stockMessage}
                  </p>
                </div>
                <div className="col-span-2">
                  <p className="text-gray-500 dark:text-gray-400">Alamat</p>
                  <p className="font-medium dark:text-gray-200">{rental.address || '-'}</p>
                </div>
                {rental.notes && (
                  <div className="col-span-2">
                    <p className="text-gray-500 dark:text-gray-400">Catatan Pelanggan</p>
                    <p className="font-medium dark:text-gray-200">{rental.notes}</p>
                  </div>
                )}
              </div>
            </div>

            {!isStockSufficient && (
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4 text-red-600 dark:text-red-400">
                <h4 className="font-medium">Peringatan: Stok Tidak Mencukupi</h4>
                <p className="text-sm mt-1">
                  Penyewaan ini tidak dapat dikonfirmasi karena stok produk tidak mencukupi.
                  Tambah stok produk terlebih dahulu atau batalkan penyewaan ini.
                </p>
                <div className="mt-2">
                  <Link href={`/admin/products/${rental.productId}`}>
                    <Button variant="outline" size="sm" className="text-xs dark:border-red-800 dark:text-red-400 dark:hover:bg-red-900/30">
                      Edit Stok Produk
                    </Button>
                  </Link>
                </div>
              </div>
            )}

            <form action={confirmRental}>
              <input type="hidden" name="rentalId" value={id} />

              <div className="space-y-3">
                <label htmlFor="adminNotes" className="text-sm font-medium dark:text-gray-300">
                  Catatan Admin (Opsional)
                </label>
                <Textarea
                  id="adminNotes"
                  name="adminNotes"
                  placeholder="Tambahkan catatan atau instruksi untuk tim operasional..."
                  className="h-32 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-200 dark:placeholder:text-gray-500"
                />
              </div>

              <div className="flex justify-end gap-3 mt-6">
                <Link href={`/admin/rentals/${id}`}>
                  <Button variant="outline" className="dark:border-gray-700 dark:text-gray-200 dark:hover:bg-gray-800">Batal</Button>
                </Link>
                <Button
                  type="submit"
                  disabled={!isStockSufficient}
                  className={`${!isStockSufficient ? 'opacity-50 cursor-not-allowed' : ''} dark:bg-blue-600 dark:hover:bg-blue-700 dark:text-white`}
                >
                  Konfirmasi Penyewaan
                </Button>
              </div>
            </form>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}