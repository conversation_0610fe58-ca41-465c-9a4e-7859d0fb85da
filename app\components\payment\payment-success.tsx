"use client";

import Link from "next/link";
import { useRouter } from "next/navigation";
import { Button } from "@/app/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card";
import { LuArrowLeft, LuCheck } from "react-icons/lu";
import { formatCurrency } from "@/lib/utils/format";

interface PaymentSuccessProps {
  orderDetails: {
    id: string;
    productName: string;
    totalAmount: number;
    depositAmount: number;
    remainingAmount: number;
    paymentType?: "deposit" | "remaining" | "full";
  };
}

export function PaymentSuccess({ orderDetails }: PaymentSuccessProps) {
  const router = useRouter();
  const { 
    id, 
    productName, 
    totalAmount, 
    depositAmount, 
    remainingAmount,
    paymentType = "deposit" 
  } = orderDetails;

  const isDepositPayment = paymentType === "deposit";
  const isFullPayment = paymentType === "full";

  return (
    <>
      <div className="flex items-center mb-8">
        <Link href="/user/dashboard" className="mr-4">
          <Button variant="outline" size="sm" className="flex items-center dark:border-gray-700 dark:text-gray-200 dark:hover:bg-gray-800">
            <LuArrowLeft className="mr-2 h-4 w-4" />
            Kembali ke Dashboard
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Pembayaran Berhasil</h1>
          <p className="text-muted-foreground">
            Terima kasih atas pembayaran Anda
          </p>
        </div>
      </div>

      <Card className="max-w-3xl mx-auto">
        <CardHeader className="text-center pb-6">
          <div className="flex justify-center mb-4">
            <div className="h-16 w-16 rounded-full bg-green-100 flex items-center justify-center">
              <LuCheck className="h-10 w-10 text-green-600" />    
            </div>
          </div>
          <CardTitle className="text-2xl">
            {isDepositPayment 
              ? "Pembayaran Uang Muka Berhasil" 
              : isFullPayment 
                ? "Pembayaran Lunas" 
                : "Pelunasan Berhasil"}
          </CardTitle>
          <p className="text-muted-foreground mt-2">Nomor Pesanan: {id.substring(0, 8)}</p>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="rounded-md bg-gray-50 dark:bg-gray-800 p-4">
            <h3 className="font-medium mb-2 dark:text-gray-200">Rincian Pembayaran</h3>
            <div className="space-y-2">
              <div className="flex justify-between dark:text-gray-300">
                <span>Produk</span>
                <span className="font-medium dark:text-gray-200">{productName}</span>
              </div>
              <div className="flex justify-between dark:text-gray-300">
                <span>Total Biaya</span>
                <span className="font-medium dark:text-gray-200">
                  {formatCurrency(totalAmount)}
                </span>
              </div>
              {isDepositPayment && (
                <>
                  <div className="flex justify-between text-green-600 dark:text-green-400">
                    <span>Uang Muka (Sudah Dibayar)</span>
                    <span className="font-medium">
                      {formatCurrency(depositAmount)}
                    </span>
                  </div>
                  <div className="flex justify-between pt-2 border-t dark:border-gray-700 mt-2 dark:text-gray-300">
                    <span>Sisa Pembayaran</span>
                    <span className="font-medium dark:text-gray-200">
                      {formatCurrency(remainingAmount)}
                    </span>
                  </div>
                </>
              )}
              {!isDepositPayment && !isFullPayment && (
                <div className="flex justify-between text-green-600 dark:text-green-400">
                  <span>Pelunasan (Sudah Dibayar)</span>
                  <span className="font-medium">
                    {formatCurrency(remainingAmount)}
                  </span>
                </div>
              )}
              {isFullPayment && (
                <div className="flex justify-between text-green-600 dark:text-green-400 pt-2 border-t dark:border-gray-700 mt-2">
                  <span>Total Pembayaran (Lunas)</span>
                  <span className="font-medium">
                    {formatCurrency(totalAmount)}
                  </span>
                </div>
              )}
            </div>
          </div>

          {isDepositPayment && (
            <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-md text-sm border border-blue-100 dark:border-blue-800">
              <p className="font-medium text-blue-700 dark:text-blue-300 mb-1">Informasi Penting</p>
              <p className="text-blue-600 dark:text-blue-400">
                Sisa pembayaran sebesar {formatCurrency(remainingAmount)} dapat dilunasi saat pengembalian genset.
              </p>
            </div>
          )}

          {!isDepositPayment && !isFullPayment && (
            <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-md text-sm border border-green-100 dark:border-green-800">
              <p className="font-medium text-green-700 dark:text-green-300 mb-1">Pelunasan Berhasil</p>
              <p className="text-green-600 dark:text-green-400">
                Pembayaran telah lunas. Terima kasih telah menyewa genset kami.
              </p>
            </div>
          )}

          <div className="flex flex-col space-y-3 pt-4">
            <Button 
              className="w-full" 
              onClick={() => router.push(`/user/rentals/${id}`)}
            >
              Lihat Detail Pesanan
            </Button>
            <Button 
              variant="outline" 
              className="w-full dark:border-gray-700 dark:text-gray-200 dark:hover:bg-gray-800" 
              onClick={() => router.push('/user/dashboard')}
            >
              Kembali ke Dashboard
            </Button>
          </div>
        </CardContent>
      </Card>
    </>
  );
} 
