"use client";

import Link from 'next/link';
import { LucideIcon } from 'lucide-react';
import { cn } from "@/lib/utils/cn";

interface MobileNavItemProps {
  href: string;
  title: string;
  icon: LucideIcon;
  isActive?: boolean;
  onClick?: () => void;
  className?: string;
  showIndicator?: boolean;
  animationDelay?: number;
}

export function MobileNavItem({
  href,
  title,
  icon: Icon,
  isActive = false,
  onClick,
  className,
  showIndicator = true,
  animationDelay = 0
}: MobileNavItemProps) {
  const handleClick = () => {
    // Call the onClick handler if provided (usually to close menu)
    if (onClick) {
      onClick();
    }
  };

  return (
    <Link
      href={href}
      onClick={handleClick}
      className={cn(
        "group flex items-center min-h-[44px] px-4 py-3 rounded-xl text-sm font-medium transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98]",
        "focus:outline-none focus:ring-2 focus:ring-violet-500 focus:ring-offset-2",
        "touch-manipulation", // Improves touch responsiveness
        isActive
          ? "bg-gradient-to-r from-violet-600 to-purple-600 text-white shadow-lg"
          : "text-violet-700 hover:bg-violet-50 hover:text-violet-800 dark:text-violet-300 dark:hover:bg-violet-900/20 dark:hover:text-violet-200",
        className
      )}
      style={{
        animationDelay: `${animationDelay}ms`,
        animation: 'slideInRight 0.3s ease-out forwards'
      }}
      role="menuitem"
      aria-current={isActive ? "page" : undefined}
    >
      <Icon className={cn(
        "w-5 h-5 mr-3 transition-transform duration-200",
        "group-hover:scale-110",
        isActive ? "text-white" : "text-violet-600 dark:text-violet-400"
      )} />
      <span className="flex-1">{title}</span>
      {isActive && showIndicator && (
        <div className="w-2 h-2 bg-white rounded-full ml-2 animate-pulse" />
      )}
    </Link>
  );
}

// Desktop navigation item variant
interface DesktopNavItemProps extends Omit<MobileNavItemProps, 'animationDelay'> {
  showActiveIndicator?: boolean;
}

export function DesktopNavItem({
  href,
  title,
  icon: Icon,
  isActive = false,
  onClick,
  className,
  showActiveIndicator = true
}: DesktopNavItemProps) {
  return (
    <Link
      href={href}
      onClick={onClick}
      className={cn(
        "group relative flex items-center min-h-[44px] px-4 py-2.5 rounded-xl text-sm font-medium transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98]",
        "focus:outline-none focus:ring-2 focus:ring-violet-500 focus:ring-offset-2",
        "shadow-sm hover:shadow-md",
        isActive
          ? "bg-gradient-to-r from-violet-600 to-purple-600 text-white shadow-lg hover:shadow-xl"
          : "bg-white/80 text-violet-700 hover:bg-violet-50 hover:text-violet-800 border border-violet-100 hover:border-violet-200 dark:bg-gray-800/80 dark:text-violet-300 dark:hover:bg-violet-900/20 dark:hover:text-violet-200 dark:border-violet-800 dark:hover:border-violet-700",
        className
      )}
      aria-current={isActive ? "page" : undefined}
    >
      <Icon className={cn(
        "w-4 h-4 mr-2.5 transition-all duration-200",
        "group-hover:scale-110",
        isActive
          ? "text-white"
          : "text-violet-600 group-hover:text-violet-700 dark:text-violet-400 dark:group-hover:text-violet-300"
      )} />
      <span className="relative">
        {title}
        {isActive && showActiveIndicator && (
          <div className="absolute -bottom-1 left-0 right-0 h-0.5 bg-white/50 rounded-full" />
        )}
      </span>

      {/* Active indicator dot */}
      {isActive && showActiveIndicator && (
        <div className="absolute -top-1 -right-1 w-2 h-2 bg-white rounded-full animate-pulse shadow-sm" />
      )}

      {/* Hover effect background */}
      <div className={cn(
        "absolute inset-0 rounded-xl opacity-0 transition-opacity duration-200",
        "bg-gradient-to-r from-violet-500/10 to-purple-500/10",
        "group-hover:opacity-100"
      )} />
    </Link>
  );
}
