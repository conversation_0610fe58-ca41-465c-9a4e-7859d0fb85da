import { useState, useCallback } from 'react';

/**
 * Hook untuk mendapatkan lokasi pengguna saat ini menggunakan Geolocation API
 */
const useGeoLocation = () => {
  const [coordinates, setCoordinates] = useState<{ lat: number; lng: number } | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [positionError, setPositionError] = useState<string | null>(null);

  const clearPositionError = useCallback(() => {
    setPositionError(null);
    setError(null);
  }, []);

  const getCurrentPosition = useCallback(() => {
    if (!navigator.geolocation) {
      const errorMsg = "Geolokasi tidak didukung di browser Anda.";
      setError(errorMsg);
      setPositionError(errorMsg);
      return Promise.reject(new Error(errorMsg));
    }
    
    setIsLoading(true);
    setError(null);
    setPositionError(null);
    
    return new Promise<GeolocationPosition>((resolve, reject) => {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          const newCoords = { lat: latitude, lng: longitude };
          setCoordinates(newCoords);
          setIsLoading(false);
          resolve(position);
        },
        (error) => {
          console.error("Error mendapatkan geolokasi:", error);
          let errorMessage = "Terjadi kesalahan saat mendapatkan lokasi Anda.";
          
          switch(error.code) {
            case error.PERMISSION_DENIED:
              errorMessage = "Akses lokasi ditolak. Silakan izinkan akses lokasi di browser Anda.";
              break;
            case error.POSITION_UNAVAILABLE:
              errorMessage = "Informasi lokasi tidak tersedia.";
              break;
            case error.TIMEOUT:
              errorMessage = "Waktu permintaan lokasi habis.";
              break;
          }
          
          setError(errorMessage);
          setPositionError(errorMessage);
          setIsLoading(false);
          reject(new Error(errorMessage));
        },
        { 
          enableHighAccuracy: true, 
          timeout: 10000, 
          maximumAge: 0 
        }
      );
    });
  }, []);

  return {
    coordinates,
    isLoading,
    error,
    positionError,
    getCurrentPosition,
    clearPositionError,
    setCoordinates
  };
};

export default useGeoLocation; 
