import { prisma } from "@/lib/config/prisma";
import { auth } from "@/auth";
import { redirect } from "next/navigation";
import Link from "next/link";
import { formatDate, formatCurrency } from "@/lib/utils/format";
import { Card, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/app/components/ui/table";
import { Button } from "@/app/components/ui/button";
import { Badge } from "@/app/components/ui/badge";
import { Search, MoreVertical, Eye, CheckCircle } from "lucide-react";
import { Input } from "@/app/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/app/components/ui/dropdown-menu";
import { RentalsTable } from "@/app/components/admin/rentals-table";

// Pastikan halaman selalu up-to-date
export const dynamic = 'force-dynamic';

// Fungsi untuk mendapatkan nama status dalam bahasa Indonesia
function getStatusName(status: string): string {
  // Konversi status ke uppercase untuk konsistensi
  const normalizedStatus = status.toUpperCase();

  const statusMap: Record<string, string> = {
    'PENDING': 'Menunggu',
    'CONFIRMED': 'Dikonfirmasi',
    'ACTIVE': 'Operasional',
    'COMPLETED': 'Selesai',
    'CANCELLED': 'Dibatalkan',
    // Alias untuk kompatibilitas
    'OPERATIONAL': 'Operasional',
    'CANCELED': 'Dibatalkan',
  };

  return statusMap[normalizedStatus] || status;
}

// Fungsi untuk mendapatkan warna status
function getStatusColor(status: string): string {
  // Konversi status ke uppercase untuk konsistensi
  const normalizedStatus = status.toUpperCase();

  const statusColorMap: Record<string, string> = {
    'PENDING': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
    'CONFIRMED': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
    'ACTIVE': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
    'COMPLETED': 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
    'CANCELLED': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
    // Alias untuk kompatibilitas
    'OPERATIONAL': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
    'CANCELED': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
  };

  return statusColorMap[normalizedStatus] || 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300';
}

export default async function AdminRentalsPage({
  searchParams
}: {
  searchParams: { q?: string }
}) {
  const session = await auth();

  // Redirect ke login jika tidak ada session atau bukan admin
  if (!session?.user || session.user.role !== "ADMIN") {
    redirect('/login');
  }

  // Ambil parameter pencarian dan tunggu resolusinya
  const params = await Promise.resolve(searchParams);
  const searchQuery = params.q || '';

  // Ambil data penyewaan dari database
  const rentals = await prisma.rental.findMany({
    where: {
      OR: [
        { product: { name: { contains: searchQuery, mode: 'insensitive' } } },
        { address: { contains: searchQuery, mode: 'insensitive' } },
        { notes: { contains: searchQuery, mode: 'insensitive' } }
      ]
    },
    include: {
      product: true,
      user: {
        select: {
          name: true,
          email: true,
          phone: true
        }
      },
      payment: true
    },
    orderBy: {
      startDate: 'desc'
    }
  });

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Daftar Penyewaan</h1>
        <div className="flex items-center gap-2">
          <form className="relative" action="/admin/rentals">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              type="search"
              name="q"
              placeholder="Cari penyewaan..."
              className="pl-9 w-[250px]"
              defaultValue={searchQuery}
            />
          </form>
          <Link href="/admin/operations">
            <Button variant="outline" className="dark:border-gray-700 dark:text-gray-200 dark:hover:bg-gray-800">Lihat Operasi</Button>
          </Link>
        </div>
      </div>

      <RentalsTable
        rentals={rentals}
        searchQuery={searchQuery}
      />
    </div>
  );
}
