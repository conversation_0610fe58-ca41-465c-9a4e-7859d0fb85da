export interface User {
    id: string;
    name: string | null;
    email: string;
    phone: string | null;
    role: string;
    image?: string | null;
    createdAt: Date;
    updatedAt: Date;
    emailVerified?: Date | null;
    isKnownUser?: boolean;
    totalCompletedRentals?: number;
}

// Untuk kasus partial user info
export interface UserInfo {
    id: string;
    name: string | null;
    email?: string;
} 
