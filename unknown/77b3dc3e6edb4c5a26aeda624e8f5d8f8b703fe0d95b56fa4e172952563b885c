import { Metadata } from "next";
import { auth } from "@/auth";
import { redirect } from "next/navigation";
import { prisma } from "@/lib/config/prisma";
import HomeContent from "./(marketing)/home-content";

export const metadata: Metadata = {
  title: "Rental Genset - Beranda",
  description: "Solusi rental genset terpercaya",
};

// Gunakan async component untuk fetch data
export default async function HomePage() {
  const session = await auth();

  // Redirect ke dashboard jika sudah login
  if (session?.user) {
    if (session.user.role === 'ADMIN') {
      redirect('/admin/dashboard');
    }
    redirect('/user/dashboard');
  }

  // Fetch featured products untuk home page
  const productsData = await prisma.product.findMany({
    where: {
      status: 'AVAILABLE'
    },
    include: {
      user: true,
    },
    take: 6,
    orderBy: {
      createdAt: 'desc'
    }
  });

  // Transform products data to match expected Product interface
  const products = productsData.map(product => ({
    id: product.id,
    name: product.name,
    capacity: product.capacity,
    price: product.price,
    imageUrl: product.imageUrl || undefined, // Convert null to undefined
    description: product.description || undefined,
    user: product.user ? {
      id: product.user.id,
      name: product.user.name,
      email: product.user.email,
      image: product.user.image,
      role: product.user.role
    } : undefined
  }));

  return <HomeContent products={products} />;
}

export function Home() {
  return (
    <main>
      <HomeContent />
    </main>
  );
}
