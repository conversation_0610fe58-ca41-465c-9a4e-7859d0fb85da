export type Role = "ADMIN" | "USER";

// Jika ingin menggunakan enum
export const Role = {
    admin: "ADMIN" as Role,
    user: "USER" as Role
} as const;

export interface AuthResult {
    success: boolean;
    message: string;
    redirectUrl?: string;
}

export interface SignUpResult {
    message: string;
    error?: {
        name?: string[];
        email?: string[];
        password?: string[];
        confirmPassword?: string[];
        _form?: string[];
    };
}
