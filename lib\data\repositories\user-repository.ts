import { User, UserRole } from '@prisma/client';
import { db } from '../db';

export class UserRepository {
  async findById(id: string): Promise<User | null> {
    return db.user.findUnique({
      where: { id }
    });
  }

  async findByEmail(email: string): Promise<User | null> {
    return db.user.findUnique({
      where: { email }
    });
  }

  async findAll(options?: {
    role?: UserRole,
    limit?: number,
    offset?: number
  }): Promise<User[]> {
    return db.user.findMany({
      where: {
        role: options?.role
      },
      take: options?.limit,
      skip: options?.offset,
      orderBy: {
        createdAt: 'desc'
      }
    });
  }

  async create(data: Omit<User, 'id' | 'createdAt' | 'updatedAt'>): Promise<User> {
    return db.user.create({
      data: {
        ...data,
        role: data.role || 'USER'
      }
    });
  }

  async update(id: string, data: Partial<Omit<User, 'id' | 'createdAt' | 'updatedAt'>>): Promise<User> {
    return db.user.update({
      where: { id },
      data
    });
  }

  async delete(id: string): Promise<User> {
    return db.user.delete({
      where: { id }
    });
  }

  async countUsers(): Promise<number> {
    return db.user.count();
  }

  async countByRole(): Promise<Array<{ role: string, count: number }>> {
    const users = await db.user.groupBy({
      by: ['role'],
      _count: {
        id: true
      }
    });

    return users.map(item => ({
      role: item.role,
      count: item._count.id
    }));
  }
}
