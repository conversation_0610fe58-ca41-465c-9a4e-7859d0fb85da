import { Metada<PERSON> } from "next";
import Link from "next/link";
import { auth } from "@/auth";
import { redirect } from "next/navigation";
import { Button } from "@/app/components/ui/button";
import { LuArrowRight, LuCalendar, LuClock, LuPackage, LuShoppingCart } from "react-icons/lu";
import { RentalTrendChart } from "@/app/components/ui/rental-trend-chart";
import { prisma } from "@/lib/config/prisma";
import { subMonths } from "date-fns";

export const metadata: Metadata = {
  title: "User Dashboard",
  description: "Dashboard pengguna Rental Genset",
};

interface RecentRental {
  id: string;
  productName: string;
  date: string | Date;
  status: string;
}

// Define interface for rental data returned from Prisma
interface RentalData {
  id: string;
  product: {
    name: string;
  };
  createdAt: Date;
  status: string;
}

async function getUserStats(userId: string) {
  try {
    // Get the start date for monthly stats (12 months ago)
    const startDate = subMonths(new Date(), 11);
    startDate.setDate(1); // Start of the month

    // Execute all independent queries in parallel for better performance
    const [activeRentalsCount, pendingPaymentsCount, recommendedProductsCount, recentRentalsData] = await Promise.all([
      // Active rentals count
      prisma.rental.count({
        where: {
          userId: userId,
          status: "ACTIVE",
        },
      }),

      // Pending payments count
      prisma.payment.count({
        where: {
          rental: {
            userId: userId,
          },
          status: "DEPOSIT_PENDING",
        },
      }),

      // Count of available products
      prisma.product.count({
        where: {
          status: "AVAILABLE",
        },
      }),

      // Recent rentals (with their products)
      prisma.rental.findMany({
        where: {
          userId: userId,
        },
        include: {
          product: {
            select: {
              name: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
        take: 3,
      }),
    ]);

    // Get all rentals from the last 12 months in a single query
    const rentalsByMonth = await prisma.rental.findMany({
      where: {
        userId: userId,
        createdAt: {
          gte: startDate,
        },
      },
      select: {
        createdAt: true,
      },
      orderBy: {
        createdAt: "asc",
      },
    });

    // Process monthly rental data client-side
    const monthlyRentals = [];
    const monthNamesIndonesia = ["Jan", "Feb", "Mar", "Apr", "Mei", "Jun", "Jul", "Agu", "Sep", "Okt", "Nov", "Des"];

    for (let i = 0; i < 12; i++) {
      const currentMonth = subMonths(new Date(), 11 - i);
      const monthIndex = currentMonth.getMonth();
      const monthName = monthNamesIndonesia[monthIndex];

      // Count rentals for this month
      const count = rentalsByMonth.filter(rental => {
        const rentalDate = new Date(rental.createdAt);
        return rentalDate.getMonth() === currentMonth.getMonth() &&
          rentalDate.getFullYear() === currentMonth.getFullYear();
      }).length;

      monthlyRentals.push({
        month: monthName,
        count,
      });
    }

    // Format recent rentals
    const formattedRecentRentals = recentRentalsData.map((rental: RentalData) => ({
      id: rental.id,
      productName: rental.product.name,
      date: rental.createdAt,
      status: rental.status,
    }));

    console.log('Stats fetched successfully from Prisma');
    console.log('Monthly rentals data:', monthlyRentals);

    return {
      activeRentals: activeRentalsCount,
      pendingPayments: pendingPaymentsCount,
      recommendedProducts: recommendedProductsCount,
      monthlyRentals,
      recentRentals: formattedRecentRentals,
    };
  } catch (error) {
    console.error('Error loading user stats from Prisma:', error);
    return {
      activeRentals: 0,
      pendingPayments: 0,
      recommendedProducts: 0,
      monthlyRentals: [],
      recentRentals: [],
    };
  }
}

export default async function UserDashboardPage() {
  const session = await auth();
  if (!session?.user) {
    redirect('/login');
  }

  const stats = await getUserStats(session.user.id);

  return (
    <>
      <div className="relative bg-gradient-to-r from-violet-50 to-indigo-50 dark:from-violet-950/40 dark:to-indigo-950/40 rounded-xl mb-8 p-6 shadow-sm border border-gray-100 dark:border-gray-800 overflow-hidden">
        <div className="absolute right-0 top-0 bottom-0 w-1/3 opacity-10 bg-gradient-to-r from-violet-200 to-indigo-300 dark:from-violet-800 dark:to-indigo-700"></div>
        <div className="relative">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div className="flex-1">

              <h1 className="text-2xl md:text-3xl font-bold tracking-tight text-white">
                Selamat datang, {session.user.name || 'Pengguna'}
              </h1>
              <p className="text-white mt-2 max-w-xl">
                Kelola penyewaan genset Anda dengan mudah. Lihat status penyewaan, pembayaran, dan temukan produk baru.
              </p>
            </div>
            <div className="flex flex-wrap items-center gap-3 mt-2 md:mt-0">
              <Link href="/user/rentals" className="hidden sm:block">
                <Button variant="outline" size="mobile">
                  <LuShoppingCart className="mr-2 h-4 w-4" />
                  Rental Saya
                </Button>
              </Link>
              <Link href="/user/catalog">
                <Button variant="gradient" size="mobile">
                  <LuPackage className="mr-2 h-4 w-4" />
                  Sewa Genset
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <div className="relative overflow-hidden rounded-xl bg-white dark:bg-gray-800 shadow-md border border-gray-100 dark:border-gray-700 transition-all duration-200 hover:shadow-lg">
          <div className="absolute top-0 right-0 h-16 w-16 bg-green-100 dark:bg-green-900/30 -mt-6 -mr-6 rounded-full" />
          <div className="absolute top-0 right-0 h-8 w-8 bg-green-200 dark:bg-green-800/40 -mt-2 -mr-10 rounded-full" />
          <div className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="rounded-full p-3 bg-green-100 dark:bg-green-900/30">
                <LuClock className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
              <div className={`rounded-full px-2 py-1 text-xs font-medium ${stats.activeRentals > 0 ? 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-100' : 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-300'}`}>
                {stats.activeRentals > 0 ? 'Aktif' : 'Tidak Ada'}
              </div>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-1">Rental Aktif</h3>
            <div className="flex items-end justify-between">
              <div>
                <p className="text-3xl font-bold text-gray-900 dark:text-white">
                  {stats.activeRentals}
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  {stats.activeRentals === 0
                    ? "Belum ada penyewaan berjalan"
                    : `Penyewaan yang sedang berjalan`}
                </p>
              </div>
              <Link href="/user/rentals" className="flex items-center gap-1 text-sm font-medium text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300 transition-colors">
                Lihat <LuArrowRight className="h-4 w-4" />
              </Link>
            </div>
          </div>
        </div>

        <div className="relative overflow-hidden rounded-xl bg-white dark:bg-gray-800 shadow-md border border-gray-100 dark:border-gray-700 transition-all duration-200 hover:shadow-lg">
          <div className="absolute top-0 right-0 h-16 w-16 bg-orange-100 dark:bg-orange-900/30 -mt-6 -mr-6 rounded-full" />
          <div className="absolute top-0 right-0 h-8 w-8 bg-orange-200 dark:bg-orange-800/40 -mt-2 -mr-10 rounded-full" />
          <div className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="rounded-full p-3 bg-orange-100 dark:bg-orange-900/30">
                <LuCalendar className="h-6 w-6 text-orange-600 dark:text-orange-400" />
              </div>
              <div className={`rounded-full px-2 py-1 text-xs font-medium ${stats.pendingPayments > 0 ? 'bg-orange-100 text-orange-800 dark:bg-orange-900/50 dark:text-orange-100' : 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-300'}`}>
                {stats.pendingPayments > 0 ? 'Menunggu' : 'Tidak Ada'}
              </div>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-1">Pembayaran Tertunda</h3>
            <div className="flex items-end justify-between">
              <div>
                <p className="text-3xl font-bold text-gray-900 dark:text-white">
                  {stats.pendingPayments}
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  {stats.pendingPayments === 0
                    ? "Tidak ada pembayaran tertunda"
                    : `Memerlukan tindakan Anda`}
                </p>
              </div>
              <Link href="/user/payments" className="flex items-center gap-1 text-sm font-medium text-orange-600 dark:text-orange-400 hover:text-orange-700 dark:hover:text-orange-300 transition-colors">
                Bayar <LuArrowRight className="h-4 w-4" />
              </Link>
            </div>
          </div>
        </div>

        <div className="relative overflow-hidden rounded-xl bg-white dark:bg-gray-800 shadow-md border border-gray-100 dark:border-gray-700 transition-all duration-200 hover:shadow-lg">
          <div className="absolute top-0 right-0 h-16 w-16 bg-blue-100 dark:bg-blue-900/30 -mt-6 -mr-6 rounded-full" />
          <div className="absolute top-0 right-0 h-8 w-8 bg-blue-200 dark:bg-blue-800/40 -mt-2 -mr-10 rounded-full" />
          <div className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="rounded-full p-3 bg-blue-100 dark:bg-blue-900/30">
                <LuPackage className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div className={`rounded-full px-2 py-1 text-xs font-medium ${stats.recommendedProducts > 0 ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-100' : 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-300'}`}>
                {stats.recommendedProducts > 0 ? 'Tersedia' : 'Kosong'}
              </div>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-1">Produk Tersedia</h3>
            <div className="flex items-end justify-between">
              <div>
                <p className="text-3xl font-bold text-gray-900 dark:text-white">
                  {stats.recommendedProducts}
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  {stats.recommendedProducts === 0
                    ? "Tidak ada produk baru"
                    : `Genset siap untuk disewa`}
                </p>
              </div>
              <Link href="/user/catalog" className="flex items-center gap-1 text-sm font-medium text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors">
                Lihat <LuArrowRight className="h-4 w-4" />
              </Link>
            </div>
          </div>
        </div>
      </div>

      <div className="grid gap-8 md:grid-cols-2 mt-10">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md border border-gray-100 dark:border-gray-700 overflow-hidden">
          <div className="p-6 border-b border-gray-100 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-violet-600 dark:text-violet-400">Aktivitas Sewa</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">Jumlah penyewaan dalam 12 bulan terakhir</p>
              </div>
              <div className="flex items-center">
                <span className="px-2.5 py-1 rounded-md bg-violet-100 dark:bg-violet-900/30 text-violet-700 dark:text-violet-300 text-xs font-medium">
                  {new Date().getFullYear()} • 12 Bulan Terakhir
                </span>
              </div>
            </div>
          </div>
          <div className="p-6">
            <RentalTrendChart data={stats.monthlyRentals || [
              { month: "Jan", count: 0 },
              { month: "Feb", count: 0 },
              { month: "Mar", count: 0 },
              { month: "Apr", count: 0 },
              { month: "Mei", count: 0 },
              { month: "Jun", count: 0 },
              { month: "Jul", count: 0 },
              { month: "Agu", count: 0 },
              { month: "Sep", count: 0 },
              { month: "Okt", count: 0 },
              { month: "Nov", count: 0 },
              { month: "Des", count: 0 },
            ]} />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md border border-gray-100 dark:border-gray-700 overflow-hidden">
          <div className="p-6 border-b border-gray-100 dark:border-gray-700">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-5 gap-2">
              <div>
                <h3 className="text-lg font-semibold text-violet-600 dark:text-violet-400">Aktivitas Sewa</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">Transaksi terbaru Anda</p>
              </div>
              <Link href="/user/rentals" className="text-sm font-medium text-violet-600 dark:text-violet-400 flex items-center hover:text-violet-700 dark:hover:text-violet-300 transition-colors">
                <span className="text-sm">Lihat Semua</span> <LuArrowRight className="ml-1 h-4 w-4" />
              </Link>
            </div>
          </div>

          {stats.recentRentals && stats.recentRentals.length > 0 ? (
            <div className="divide-y divide-gray-100 dark:divide-gray-700">
              {stats.recentRentals.map((rental: RecentRental) => (
                <div key={rental.id} className="p-4 hover:bg-violet-50 dark:hover:bg-violet-900/10 transition-colors">
                  <div className="flex flex-wrap items-center gap-4">
                    <div className="shrink-0">
                      <div className={`w-10 h-10 rounded-full flex items-center justify-center ${rental.status === "ACTIVE" ? 'bg-green-100 dark:bg-green-900/30' : rental.status === "PENDING" ? 'bg-yellow-100 dark:bg-yellow-900/30' : rental.status === "COMPLETED" ? 'bg-blue-100 dark:bg-blue-900/30' : 'bg-gray-100 dark:bg-gray-800'}`}>
                        <LuShoppingCart className={`h-5 w-5 ${rental.status === "ACTIVE" ? 'text-green-600 dark:text-green-400' : rental.status === "PENDING" ? 'text-yellow-600 dark:text-yellow-400' : rental.status === "COMPLETED" ? 'text-blue-600 dark:text-blue-400' : 'text-gray-500 dark:text-gray-400'}`} />
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex flex-wrap items-center justify-between gap-2">
                        <p className="text-sm font-medium text-gray-900 dark:text-white truncate">{rental.productName}</p>
                        <div className={`rounded-full px-2.5 py-0.5 text-xs font-medium ${rental.status === "ACTIVE" ? 'bg-green-100 text-green-700 dark:bg-green-900/50 dark:text-green-300' : rental.status === "PENDING" ? 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/50 dark:text-yellow-300' : rental.status === "COMPLETED" ? 'bg-blue-100 text-blue-700 dark:bg-blue-900/50 dark:text-blue-300' : 'bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-300'}`}>
                          {rental.status === "ACTIVE" ? "Aktif" :
                            rental.status === "PENDING" ? "Tertunda" :
                              rental.status === "COMPLETED" ? "Selesai" :
                                rental.status}
                        </div>
                      </div>
                      <div className="flex items-center gap-2 mt-1">
                        <div className="text-xs text-gray-500 dark:text-gray-400 flex items-center gap-1">
                          <LuCalendar className="h-3 w-3 text-violet-500 dark:text-violet-400" />
                          {new Date(rental.date).toLocaleDateString('id-ID', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric',
                          })}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center p-8 text-center">
              <div className="bg-gray-100 dark:bg-gray-700 p-4 rounded-full mb-4">
                <LuShoppingCart className="h-8 w-8 text-gray-500 dark:text-gray-400" />
              </div>
              <h4 className="text-gray-900 dark:text-white font-medium mb-1">Belum ada riwayat rental</h4>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">Mulailah sewa genset pertama Anda sekarang</p>
              <Link href="/user/catalog">
                <Button variant="gradient" size="mobile">
                  <LuPackage className="mr-2 h-4 w-4" />
                  Sewa Sekarang
                </Button>
              </Link>
            </div>
          )}
        </div>
      </div>
    </>
  );
}
