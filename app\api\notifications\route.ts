import { prisma } from "@/lib/config/prisma";
import { auth } from "@/auth";
import { NextResponse } from "next/server";

// Enum values sebagai string literals
const NotificationTypes = {
  PAYMENT_SUCCESS: 'PAYMENT_SUCCESS',
  PAYMENT_FAILED: 'PAYMENT_FAILED',
  NEW_RENTAL: 'NEW_RENTAL',
  RENTAL_CONFIRMED: 'RENTAL_CONFIRMED',
  OPERATION_STARTED: 'OPERATION_STARTED',
  OPERATION_COMPLETED: 'OPERATION_COMPLETED',
  LOW_STOCK: 'LOW_STOCK',
  OVERTIME_DETECTED: 'OVERTIME_DETECTED',
  NEW_PAYMENT: 'NEW_PAYMENT',
  NEW_INVOICE: 'NEW_INVOICE'
} as const;

type NotificationType = typeof NotificationTypes[keyof typeof NotificationTypes];

export async function GET() {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const notifications = await prisma.notification.findMany({
      where: {
        userId: session.user.id,
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 20 // Ambil 20 notifikasi terbaru
    }).catch(err => {
      console.error("[PRISMA_QUERY_ERROR]", err);
      throw new Error("Database query failed");
    });

    if (!notifications) {
      return NextResponse.json({
        items: [],
        unreadCount: 0
      });
    }

    return NextResponse.json({
      items: notifications,
      unreadCount: notifications.filter(n => !n.isRead).length
    });
  } catch (error) {
    console.error("[GET_NOTIFICATIONS_ERROR]", error);
    return NextResponse.json(
      { error: "Gagal mengambil notifikasi", details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const session = await auth();
    // Cek apakah user adalah admin
    if (!session?.user || session.user.role !== "ADMIN") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { userId, title, message, type } = await request.json();

    // Validasi tipe notifikasi
    const isValidType = Object.values(NotificationTypes).includes(type as NotificationType);
    if (!isValidType) {
      return NextResponse.json(
        { error: "Tipe notifikasi tidak valid" },
        { status: 400 }
      );
    }

    const notification = await prisma.notification.create({
      data: {
        userId,
        title,
        message,
        type: type as NotificationType,
        isRead: false
      }
    });

    // Di sini bisa ditambahkan integrasi dengan sistem notifikasi real-time
    // seperti Pusher atau WebSocket

    return NextResponse.json(notification);
  } catch (error) {
    console.error("[NOTIFICATION_ERROR]", error);
    return NextResponse.json(
      { error: "Gagal membuat notifikasi" },
      { status: 500 }
    );
  }
}
