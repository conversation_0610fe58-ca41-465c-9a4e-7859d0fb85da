import { auth } from "@/auth";
import { prisma } from "@/lib/config/prisma";
import { notFound } from "next/navigation";
import { PaymentStatus } from "@/app/components/payment/payment-status";
import { PaymentForm } from "@/app/components/payment/payment-form";

interface RemainingPaymentPageProps {
  params: {
    id: string;
  };
}

export default async function RemainingPaymentPage({ params }: RemainingPaymentPageProps) {
  const session = await auth();
  if (!session?.user) {
    return notFound();
  }

  const { id } = await Promise.resolve(params);

  const rental = await prisma.rental.findUnique({
    where: {
      id,
      userId: session.user.id
    },
    include: {
      product: true,
      payment: true
    }
  });

  if (!rental || !rental.payment || rental.payment.status !== "DEPOSIT_PAID") {
    return notFound();
  }

  return (
    <div className="container mx-auto py-8">
      <div className="max-w-3xl mx-auto">
        <h1 className="text-3xl font-bold mb-6"><PERSON><PERSON><PERSON><PERSON></h1>
        <PaymentStatus 
          rental={{
            id: rental.id,
            status: rental.status,
            amount: rental.amount,
            product: {
              name: rental.product.name
            },
            payment: rental.payment
          }}
        />
        
        <PaymentForm
          rentalId={rental.id}
          amount={rental.payment.remaining + (rental.payment.overtime || 0)}
          type="remaining"
        />
      </div>
    </div>
  );
} 