"use client";

import { 
  NavSkeleton, 
  MobileMenuSkeleton, 
  UserProfileSkeleton, 
  NavigationSkeleton,
  NavItemSkeleton,
  UserProfileDropdownSkeleton
} from "./nav-skeleton";
import { cn } from "@/lib/utils/cn";

// Example: Complete navigation header loading state
export function NavigationHeaderLoading({ className }: { className?: string }) {
  return (
    <header className={cn(
      "sticky top-0 z-50 w-full bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl shadow-md border-b border-violet-100 dark:border-violet-800",
      className
    )}>
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16 lg:h-20">
          {/* Logo Skeleton */}
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-r from-violet-100/60 to-purple-100/60 dark:from-violet-900/20 dark:to-purple-900/20 rounded-xl animate-pulse" />
            <div className="hidden sm:block">
              <div className="h-6 w-32 bg-gradient-to-r from-violet-100/60 to-purple-100/60 dark:from-violet-900/20 dark:to-purple-900/20 rounded-lg animate-pulse" />
            </div>
          </div>

          {/* Navigation Skeleton */}
          <NavigationSkeleton />
        </div>
      </div>
    </header>
  );
}

// Example: Mobile menu loading state
export function MobileMenuLoadingExample({ isOpen }: { isOpen: boolean }) {
  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div className="fixed inset-0 bg-black/20 backdrop-blur-sm z-40 animate-pulse" />
      
      {/* Mobile Menu Skeleton */}
      <MobileMenuSkeleton />
    </>
  );
}

// Example: Navigation items loading with stagger animation
export function NavigationItemsLoading({ 
  itemCount = 4, 
  variant = "desktop" as "mobile" | "desktop" | "tablet"
}: { 
  itemCount?: number;
  variant?: "mobile" | "desktop" | "tablet";
}) {
  return (
    <div className={cn(
      "flex",
      variant === "mobile" ? "flex-col space-y-2 p-4" : "items-center space-x-2"
    )}>
      {Array.from({ length: itemCount }).map((_, index) => (
        <div
          key={index}
          className={cn(
            variant === "mobile" && `skeleton-stagger-${index + 1}`
          )}
        >
          <NavItemSkeleton
            variant={variant}
            showIcon={true}
            showIndicator={index === 0} // First item shows as active
          />
        </div>
      ))}
    </div>
  );
}

// Example: User profile with dropdown loading
export function UserProfileLoadingExample({ showDropdown = false }: { showDropdown?: boolean }) {
  return (
    <div className="relative">
      <UserProfileSkeleton />
      {showDropdown && <UserProfileDropdownSkeleton />}
    </div>
  );
}

// Example: Responsive navigation loading that adapts to screen size
export function ResponsiveNavigationLoading({ className }: { className?: string }) {
  return (
    <div className={cn("w-full", className)}>
      {/* Mobile Navigation (≤768px) */}
      <div className="md:hidden flex items-center justify-between p-4">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-violet-100/80 dark:bg-violet-900/30 rounded-lg animate-pulse" />
          <div className="h-5 w-24 bg-violet-100/80 dark:bg-violet-900/30 rounded-lg animate-pulse" />
        </div>
        <NavSkeleton variant="mobile" />
      </div>

      {/* Tablet Navigation (769px-1023px) */}
      <div className="hidden md:flex lg:hidden items-center justify-between p-4">
        <div className="flex items-center gap-3">
          <div className="w-9 h-9 bg-violet-100/80 dark:bg-violet-900/30 rounded-xl animate-pulse" />
          <div className="h-6 w-28 bg-violet-100/80 dark:bg-violet-900/30 rounded-lg animate-pulse" />
        </div>
        <div className="flex items-center gap-4">
          <NavSkeleton variant="tablet" />
          <UserProfileSkeleton />
        </div>
      </div>

      {/* Desktop Navigation (≥1024px) */}
      <div className="hidden lg:flex items-center justify-between p-6">
        <div className="flex items-center gap-4">
          <div className="w-10 h-10 bg-violet-100/80 dark:bg-violet-900/30 rounded-xl animate-pulse" />
          <div className="h-7 w-32 bg-violet-100/80 dark:bg-violet-900/30 rounded-lg animate-pulse" />
        </div>
        <div className="flex items-center gap-6">
          <NavSkeleton variant="desktop" />
          <UserProfileSkeleton />
        </div>
      </div>
    </div>
  );
}

// Example: Loading state for navigation with realistic timing
export function NavigationLoadingWithTiming({ 
  className,
  loadingStage = "initial" 
}: { 
  className?: string;
  loadingStage?: "initial" | "navigation" | "profile" | "complete";
}) {
  return (
    <div className={cn("w-full transition-all duration-300", className)}>
      {/* Stage 1: Initial load */}
      {loadingStage === "initial" && (
        <div className="flex items-center justify-between p-4 lg:p-6">
          <div className="w-32 h-8 bg-violet-100/60 dark:bg-violet-900/20 rounded-xl animate-pulse" />
          <div className="w-16 h-8 bg-violet-100/60 dark:bg-violet-900/20 rounded-xl animate-pulse" />
        </div>
      )}

      {/* Stage 2: Navigation loading */}
      {loadingStage === "navigation" && (
        <div className="flex items-center justify-between p-4 lg:p-6">
          <div className="w-32 h-8 bg-violet-600/20 rounded-xl" />
          <NavSkeleton variant="desktop" />
          <div className="w-16 h-8 bg-violet-100/60 dark:bg-violet-900/20 rounded-xl animate-pulse" />
        </div>
      )}

      {/* Stage 3: Profile loading */}
      {loadingStage === "profile" && (
        <div className="flex items-center justify-between p-4 lg:p-6">
          <div className="w-32 h-8 bg-violet-600/20 rounded-xl" />
          <nav className="flex items-center space-x-2">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="h-11 w-24 bg-violet-600/20 rounded-xl" />
            ))}
          </nav>
          <UserProfileSkeleton />
        </div>
      )}

      {/* Stage 4: Complete (fade out) */}
      {loadingStage === "complete" && (
        <div className="opacity-0 transition-opacity duration-500">
          <NavigationSkeleton />
        </div>
      )}
    </div>
  );
}

// Example: Skeleton with shimmer effect
export function NavigationSkeletonWithShimmer({ className }: { className?: string }) {
  return (
    <div className={cn("w-full", className)}>
      <div className="flex items-center justify-between p-4 lg:p-6">
        <div className="w-32 h-8 skeleton-shimmer rounded-xl" />
        <div className="flex items-center space-x-2">
          {Array.from({ length: 4 }).map((_, i) => (
            <div 
              key={i} 
              className="h-11 w-24 skeleton-shimmer rounded-xl"
              style={{ animationDelay: `${i * 100}ms` }}
            />
          ))}
        </div>
        <div className="w-40 h-11 skeleton-shimmer rounded-xl" />
      </div>
    </div>
  );
}
