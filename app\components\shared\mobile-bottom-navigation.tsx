'use client';

import {
    LuLayoutDashboard as LuDashboard,
    LuShoppingCart,
    LuPackage,
} from 'react-icons/lu';
import { BottomNavigation } from './bottom-navigation';

const menuItems = [
    {
        href: '/user/dashboard',
        title: '<PERSON><PERSON><PERSON>',
        icon: LuDashboard
    },
    {
        href: '/user/catalog',
        title: 'Katalog',
        icon: LuPackage
    },
    {
        href: '/user/rentals',
        title: 'Rental',
        icon: LuShoppingCart
    }
];

export function MobileBottomNavigation() {
    return <BottomNavigation menuItems={menuItems} variant="user" />;
}
