import { auth } from "@/auth";
import { prisma } from "@/lib/config/prisma";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    // Dapatkan sesi pengguna
    const session = await auth();
    
    // Jika tidak ada sesi, kembalikan error unauthorized
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Dapatkan user dari database
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { image: true }
    });

    // Jika user tidak ditemukan, kembalikan error not found
    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    // Kembalikan URL gambar
    return NextResponse.json({
      image: user.image
    });
  } catch (error) {
    console.error("Error fetching user avatar:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
} 
