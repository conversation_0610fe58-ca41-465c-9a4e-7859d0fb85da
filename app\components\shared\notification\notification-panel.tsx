"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "../../ui/card";
import { Button } from "../../ui/button";
import { Info, AlertTriangle, DollarSign, Activity, ChevronRight } from "lucide-react";
import { Badge } from "../../ui/badge";

interface Notification {
  id: string;
  title: string;
  message: string;
  type: string;
  isRead: boolean;
  createdAt: Date;
}

interface NotificationCounts {
  total: number;
  counts: {
    rental: number;
    payment: number;
    stock: number;
    other: number;
  };
}

export function NotificationPanel() {
  const router = useRouter();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [counts, setCounts] = useState<NotificationCounts>({
    total: 0,
    counts: { rental: 0, payment: 0, stock: 0, other: 0 }
  });
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("all");
  const [hasError, setHasError] = useState(false);

  // Mengelompokkan notifikasi berdasarkan jenisnya
  const rentalNotifications = notifications.filter(
    n => n.type === "new_rental" || n.type === "rental_confirmed" || n.type === "operation_started" || n.type === "operation_completed"
  );
  
  const paymentNotifications = notifications.filter(
    n => n.type === "payment_success" || n.type === "payment_failed" || n.type === "new_payment" || n.type === "new_invoice"
  );
  
  const stockNotifications = notifications.filter(
    n => n.type === "low_stock"
  );
  
  const otherNotifications = notifications.filter(
    n => !["new_rental", "rental_confirmed", "operation_started", "operation_completed", 
           "payment_success", "payment_failed", "new_payment", "new_invoice", 
           "low_stock"].includes(n.type)
  );

  useEffect(() => {
    const fetchNotifications = async () => {
      try {
        setLoading(true);
        setHasError(false);
        
        // Ambil semua notifikasi
        const notificationsRes = await fetch('/api/notifications', {
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
        });
        
        // Jika unauthorized, jangan lanjutkan
        if (notificationsRes.status === 401) {
          console.log("User not authenticated, skipping notifications fetch");
          setNotifications([]);
          setCounts({
            total: 0,
            counts: { rental: 0, payment: 0, stock: 0, other: 0 }
          });
          return;
        }
        
        // Ambil hitungan notifikasi
        const countsRes = await fetch('/api/notifications/counts', {
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
        });
        
        if (notificationsRes.ok) {
          const data = await notificationsRes.json();
          setNotifications(data.items || []);
        } else {
          console.error("Failed to fetch notifications:", notificationsRes.status, notificationsRes.statusText);
          setHasError(true);
        }
        
        if (countsRes.ok) {
          const data = await countsRes.json();
          setCounts(data);
        } else {
          console.error("Failed to fetch notification counts:", countsRes.status, countsRes.statusText);
          setHasError(true);
        }
      } catch (error) {
        console.error("Error fetching notifications:", error);
        setHasError(true);
        // Set default values on error
        setNotifications([]);
        setCounts({
          total: 0,
          counts: { rental: 0, payment: 0, stock: 0, other: 0 }
        });
      } finally {
        setLoading(false);
      }
    };

    fetchNotifications();
    
    // Polling untuk notifikasi real-time dengan interval yang lebih panjang
    const intervalId = setInterval(() => {
      // Hanya fetch jika tidak sedang loading dan tidak ada error
      if (!loading && !hasError) {
        fetchNotifications();
      }
    }, 60000); // Setiap 60 detik untuk mengurangi beban
    
    return () => clearInterval(intervalId);
  }, [loading, hasError]);

  const markAsRead = async (id: string) => {
    try {
      const response = await fetch(`/api/notifications/${id}/read`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (response.ok) {
        // Update state setelah menandai sebagai telah dibaca
        setNotifications(notifications.map(notification =>
          notification.id === id ? { ...notification, isRead: true } : notification
        ));
        
        // Update juga hitungan
        fetchCounts();
      } else {
        console.error("Failed to mark notification as read:", response.status, response.statusText);
      }
    } catch (error) {
      console.error("Error marking notification as read:", error);
    }
  };

  const markAllAsRead = async () => {
    try {
      const response = await fetch('/api/notifications/read-all', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (response.ok) {
        // Update state setelah menandai semua sebagai telah dibaca
        setNotifications(notifications.map(notification => ({ ...notification, isRead: true })));
        
        // Reset hitungan
        setCounts({
          total: 0,
          counts: { rental: 0, payment: 0, stock: 0, other: 0 }
        });
      } else {
        console.error("Failed to mark all notifications as read:", response.status, response.statusText);
      }
    } catch (error) {
      console.error("Error marking all notifications as read:", error);
    }
  };
  
  const fetchCounts = async () => {
    try {
      const response = await fetch('/api/notifications/counts', {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (response.ok) {
        const data = await response.json();
        setCounts(data);
      } else {
        console.error("Failed to fetch notification counts:", response.status, response.statusText);
      }
    } catch (error) {
      console.error("Error fetching notification counts:", error);
    }
  };

  // Fungsi untuk menentukan URL navigasi berdasarkan tipe notifikasi
  const getNavigationUrl = (notification: Notification): string | null => {
    const { type, message } = notification;
    
    // Extract rental ID dari message (format: "ID: xxxxx" atau "(ID: xxxxx)")
    const rentalIdMatch = message.match(/\(ID:\s*([a-zA-Z0-9]+)\)|ID:\s*([a-zA-Z0-9]+)/i);
    const rentalId = rentalIdMatch ? (rentalIdMatch[1] || rentalIdMatch[2]) : null;
    
    // Extract product ID dari message untuk low stock (format: "Produk (ID: xxxxx)")
    const productIdMatch = message.match(/Produk\s*\(ID:\s*([a-zA-Z0-9]+)\)/i);
    const productId = productIdMatch ? productIdMatch[1] : null;
    
    switch (type.toLowerCase()) {
      case 'new_rental':
      case 'rental_confirmed':
        return rentalId ? `/admin/rentals/${rentalId}` : '/admin/rentals';
      
      case 'operation_started':
      case 'operation_completed':
        return rentalId ? `/admin/operations/${rentalId}` : '/admin/operations';
      
      case 'payment_success':
      case 'payment_failed':
      case 'new_payment':
      case 'new_invoice':
        // Untuk payment notifications, arahkan ke halaman payments
        return '/admin/payments';
      
      case 'low_stock':
        return productId ? `/admin/products/${productId}` : '/admin/products';
      
      case 'overtime_detected':
        return rentalId ? `/admin/operations/${rentalId}` : '/admin/operations';
      
      default:
        return null;
    }
  };

  // Handle klik notifikasi
  const handleNotificationClick = async (notification: Notification) => {
    // Tandai sebagai sudah dibaca
    await markAsRead(notification.id);
    
    // Navigasi ke halaman yang sesuai
    const url = getNavigationUrl(notification);
    if (url) {
      router.push(url);
    }
  };

  // Render notifikasi individual
  const renderNotification = (notification: Notification) => {
    const isUnread = !notification.isRead;
    
    // Tentukan ikon berdasarkan tipe notifikasi
    let icon = <Info className="h-4 w-4 text-blue-500" />;
    
    if (notification.type.includes('payment')) {
      icon = <DollarSign className="h-4 w-4 text-green-500" />;
    } else if (notification.type === 'low_stock') {
      icon = <AlertTriangle className="h-4 w-4 text-red-500" />;
    } else if (notification.type.includes('operation')) {
      icon = <Activity className="h-4 w-4 text-purple-500" />;
    }

    return (
      <div
        key={notification.id}
        className={`p-3 border-b ${isUnread ? 'bg-blue-50 dark:bg-blue-950/30' : ''} cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200 group`}
        onClick={() => handleNotificationClick(notification)}
        title="Klik untuk melihat detail"
      >
        <div className="flex gap-3">
          <div className="mt-1 flex-shrink-0">
            {icon}
          </div>
          <div className="flex-1">
            <div className="flex justify-between">
              <p className={`text-sm font-medium ${isUnread ? 'text-black dark:text-white' : 'text-gray-700 dark:text-gray-300'}`}>
                {notification.title}
              </p>
              <span className="text-xs text-gray-500 dark:text-gray-400">
                {new Date(notification.createdAt).toLocaleDateString()}
              </span>
            </div>
            <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">{notification.message}</p>
          </div>
          <div className="flex-shrink-0 mt-1">
            <ChevronRight className="h-4 w-4 text-gray-400 dark:text-gray-500 group-hover:text-gray-600 dark:group-hover:text-gray-300 transition-colors" />
          </div>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex justify-between items-center">
            <span>Notifikasi</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-40">
            <p className="text-gray-500 dark:text-gray-400">Memuat notifikasi...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (hasError) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex justify-between items-center">
            <span>Notifikasi</span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setHasError(false);
                setLoading(true);
              }}
            >
              Coba Lagi
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-40">
            <div className="text-center">
              <p className="text-gray-500 dark:text-gray-400 mb-2">Gagal memuat notifikasi</p>
              <p className="text-sm text-gray-400 dark:text-gray-500">Silakan coba lagi atau refresh halaman</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex justify-between items-center">
          <span>Notifikasi</span>
          {counts.total > 0 && (
            <Button 
              variant="outline" 
              size="sm"
              onClick={markAllAsRead}
            >
              Tandai semua dibaca
            </Button>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col">
          <div className="grid grid-cols-5 mb-4">
            <button
              onClick={() => setActiveTab("all")}
              className={`relative py-2 rounded-md transition ${activeTab === "all" ? "bg-gray-100 font-medium" : ""}`}
            >
              Semua
              {counts.total > 0 && (
                <Badge className="absolute -top-2 -right-2 px-1.5 py-0.5 min-w-4 h-4 flex items-center justify-center text-[10px] bg-red-500">
                  {counts.total > 9 ? '9+' : counts.total}
                </Badge>
              )}
            </button>
            <button
              onClick={() => setActiveTab("rental")}
              className={`relative py-2 rounded-md transition ${activeTab === "rental" ? "bg-gray-100 font-medium" : ""}`}
            >
              Penyewaan
              {counts.counts.rental > 0 && (
                <Badge className="absolute -top-2 -right-2 px-1.5 py-0.5 min-w-4 h-4 flex items-center justify-center text-[10px] bg-blue-500">
                  {counts.counts.rental > 9 ? '9+' : counts.counts.rental}
                </Badge>
              )}
            </button>
            <button
              onClick={() => setActiveTab("payment")}
              className={`relative py-2 rounded-md transition ${activeTab === "payment" ? "bg-gray-100 font-medium" : ""}`}
            >
              Pembayaran
              {counts.counts.payment > 0 && (
                <Badge className="absolute -top-2 -right-2 px-1.5 py-0.5 min-w-4 h-4 flex items-center justify-center text-[10px] bg-green-500">
                  {counts.counts.payment > 9 ? '9+' : counts.counts.payment}
                </Badge>
              )}
            </button>
            <button
              onClick={() => setActiveTab("stock")}
              className={`relative py-2 rounded-md transition ${activeTab === "stock" ? "bg-gray-100 font-medium" : ""}`}
            >
              Stok
              {counts.counts.stock > 0 && (
                <Badge className="absolute -top-2 -right-2 px-1.5 py-0.5 min-w-4 h-4 flex items-center justify-center text-[10px] bg-red-500">
                  {counts.counts.stock > 9 ? '9+' : counts.counts.stock}
                </Badge>
              )}
            </button>
            <button
              onClick={() => setActiveTab("other")}
              className={`relative py-2 rounded-md transition ${activeTab === "other" ? "bg-gray-100 font-medium" : ""}`}
            >
              Lainnya
              {counts.counts.other > 0 && (
                <Badge className="absolute -top-2 -right-2 px-1.5 py-0.5 min-w-4 h-4 flex items-center justify-center text-[10px] bg-gray-500">
                  {counts.counts.other > 9 ? '9+' : counts.counts.other}
                </Badge>
              )}
            </button>
          </div>
          
          <div className="border rounded-md overflow-hidden">
            {activeTab === "all" && (
              <div className="max-h-[400px] overflow-y-auto">
                {notifications.length === 0 ? (
                  <div className="p-4 text-center text-gray-500">Tidak ada notifikasi</div>
                ) : (
                  notifications.map(renderNotification)
                )}
              </div>
            )}
            
            {activeTab === "rental" && (
              <div className="max-h-[400px] overflow-y-auto">
                {rentalNotifications.length === 0 ? (
                  <div className="p-4 text-center text-gray-500">Tidak ada notifikasi terkait penyewaan</div>
                ) : (
                  rentalNotifications.map(renderNotification)
                )}
              </div>
            )}
            
            {activeTab === "payment" && (
              <div className="max-h-[400px] overflow-y-auto">
                {paymentNotifications.length === 0 ? (
                  <div className="p-4 text-center text-gray-500">Tidak ada notifikasi terkait pembayaran</div>
                ) : (
                  paymentNotifications.map(renderNotification)
                )}
              </div>
            )}
            
            {activeTab === "stock" && (
              <div className="max-h-[400px] overflow-y-auto">
                {stockNotifications.length === 0 ? (
                  <div className="p-4 text-center text-gray-500">Tidak ada notifikasi terkait stok</div>
                ) : (
                  stockNotifications.map(renderNotification)
                )}
              </div>
            )}
            
            {activeTab === "other" && (
              <div className="max-h-[400px] overflow-y-auto">
                {otherNotifications.length === 0 ? (
                  <div className="p-4 text-center text-gray-500">Tidak ada notifikasi lainnya</div>
                ) : (
                  otherNotifications.map(renderNotification)
                )}
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 
