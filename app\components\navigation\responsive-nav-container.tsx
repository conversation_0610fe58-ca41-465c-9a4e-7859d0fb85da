"use client";

import { cn } from "@/lib/utils/cn";
import { ReactNode } from "react";

interface ResponsiveNavContainerProps {
  children: ReactNode;
  className?: string;
  variant?: "header" | "sidebar" | "bottom";
  background?: "default" | "glass" | "solid" | "gradient";
  shadow?: "none" | "sm" | "md" | "lg" | "xl";
  border?: boolean;
  sticky?: boolean;
}

export function ResponsiveNavContainer({
  children,
  className,
  variant = "header",
  background = "glass",
  shadow = "md",
  border = true,
  sticky = true
}: ResponsiveNavContainerProps) {
  const baseClasses = "w-full transition-all duration-200";
  
  const variantClasses = {
    header: "top-0 z-50",
    sidebar: "left-0 h-full",
    bottom: "bottom-0 z-50"
  };

  const backgroundClasses = {
    default: "bg-background",
    glass: "bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl",
    solid: "bg-white dark:bg-gray-900",
    gradient: "bg-gradient-to-r from-violet-50/90 to-purple-50/90 dark:from-violet-950/90 dark:to-purple-950/90 backdrop-blur-xl"
  };

  const shadowClasses = {
    none: "",
    sm: "shadow-sm",
    md: "shadow-md",
    lg: "shadow-lg",
    xl: "shadow-xl"
  };

  const borderClasses = border 
    ? "border-b border-violet-100 dark:border-violet-800" 
    : "";

  const stickyClasses = sticky ? "sticky" : "relative";

  return (
    <div className={cn(
      baseClasses,
      variantClasses[variant],
      backgroundClasses[background],
      shadowClasses[shadow],
      borderClasses,
      stickyClasses,
      className
    )}>
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16 lg:h-20">
          {children}
        </div>
      </div>
    </div>
  );
}

// Mobile-optimized navigation wrapper
interface MobileNavWrapperProps {
  children: ReactNode;
  className?: string;
  fullHeight?: boolean;
}

export function MobileNavWrapper({
  children,
  className,
  fullHeight = false
}: MobileNavWrapperProps) {
  return (
    <div className={cn(
      "md:hidden",
      fullHeight && "min-h-screen",
      className
    )}>
      {children}
    </div>
  );
}

// Desktop navigation wrapper
interface DesktopNavWrapperProps {
  children: ReactNode;
  className?: string;
  layout?: "horizontal" | "vertical";
}

export function DesktopNavWrapper({
  children,
  className,
  layout = "horizontal"
}: DesktopNavWrapperProps) {
  return (
    <div className={cn(
      "hidden md:flex",
      layout === "horizontal" ? "items-center space-x-2" : "flex-col space-y-2",
      className
    )}>
      {children}
    </div>
  );
}

// Touch-friendly container for mobile interactions
interface TouchContainerProps {
  children: ReactNode;
  className?: string;
  minTouchTarget?: boolean;
  hapticFeedback?: boolean;
}

export function TouchContainer({
  children,
  className,
  minTouchTarget = true,
  hapticFeedback = false
}: TouchContainerProps) {
  const handleTouch = () => {
    if (hapticFeedback && 'vibrate' in navigator) {
      navigator.vibrate(10); // Light haptic feedback
    }
  };

  return (
    <div 
      className={cn(
        "touch-manipulation", // Improves touch responsiveness
        minTouchTarget && "min-h-[44px] min-w-[44px]", // iOS recommended minimum
        "select-none", // Prevents text selection on touch
        className
      )}
      onTouchStart={hapticFeedback ? handleTouch : undefined}
    >
      {children}
    </div>
  );
}

// Responsive grid container for navigation items
interface NavGridProps {
  children: ReactNode;
  className?: string;
  columns?: {
    mobile?: number;
    tablet?: number;
    desktop?: number;
  };
  gap?: "sm" | "md" | "lg";
}

export function NavGrid({
  children,
  className,
  columns = { mobile: 1, tablet: 2, desktop: 4 },
  gap = "md"
}: NavGridProps) {
  const gapClasses = {
    sm: "gap-2",
    md: "gap-4",
    lg: "gap-6"
  };

  const gridClasses = `grid grid-cols-${columns.mobile} md:grid-cols-${columns.tablet} lg:grid-cols-${columns.desktop}`;

  return (
    <div className={cn(
      gridClasses,
      gapClasses[gap],
      className
    )}>
      {children}
    </div>
  );
}
