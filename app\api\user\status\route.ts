import { NextResponse } from "next/server";
import { auth } from "@/auth";
import { prisma } from "@/lib/config/prisma";

export async function GET() {
  try {
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json({ isKnownUser: false }, { status: 401 });
    }
    
    const userId = session.user.id;
    
    // Cek jumlah rental yang sudah selesai
    const completedRentals = await prisma.rental.count({
      where: {
        userId,
        status: "COMPLETED"
      }
    });
    
    // User dianggap dikenal jika memiliki lebih dari 2 rental yang selesai
    const isKnownUser = completedRentals >= 2;
    
    return NextResponse.json({ 
      isKnownUser,
      completedRentals
    });
  } catch (error) {
    console.error("Error checking user status:", error);
    return NextResponse.json(
      { error: "Failed to check user status" },
      { status: 500 }
    );
  }
} 
