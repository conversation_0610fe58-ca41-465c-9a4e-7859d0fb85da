import { cn } from "@/lib/utils"

interface SkeletonProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: "default" | "violet" | "purple";
}

function Skeleton({
  className,
  variant = "default",
  ...props
}: SkeletonProps) {
  const variantClasses = {
    default: "bg-muted",
    violet: "bg-violet-100 dark:bg-violet-900/50",
    purple: "bg-purple-100 dark:bg-purple-900/50"
  };
  
  return (
    <div
      className={cn(
        "rounded-md w-full max-w-full",
        variantClasses[variant],
        "motion-safe:animate-pulse motion-reduce:animate-none",
        className
      )}
      {...props}
    />
  )
}

export { Skeleton }
