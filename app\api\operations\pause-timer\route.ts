import { NextResponse } from "next/server";
import { prisma } from "@/lib/config/prisma";
import { auth } from "@/auth";
import { revalidatePath } from "next/cache";

type TimerStatus = {
  rentalId: string;
  isPaused: boolean;
  pauseOffset: number;
  pauseStartTime: number;
};

// API untuk menyimpan dan mendapatkan status timer
export async function POST(request: Request) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { rentalId, isPaused, pauseOffset, pauseStartTime } = body as TimerStatus;
    
    // Validasi rental ID
    if (!rentalId) {
      return NextResponse.json(
        { error: "Rental ID is required" },
        { status: 400 }
      );
    }
    
    // Periksa apakah rental ada
    const existingRental = await prisma.rental.findUnique({
      where: { id: rentalId }
    });
    
    if (!existingRental) {
      return NextResponse.json(
        { error: "Rental not found" },
        { status: 404 }
      );
    }

    // Cek apakah metadata timer sudah ada
    const existingTimer = await prisma.rental.findUnique({
      where: { id: rentalId },
      select: { 
        id: true,
        notes: true
      }
    });

    // Update data rental dengan metadata timer di kolom notes
    // Format: TIMER_STATUS: isPaused,pauseOffset,pauseStartTime
    const timerMetadata = `TIMER_STATUS: ${isPaused},${pauseOffset},${pauseStartTime}`;
    let updatedNotes: string;

    if (existingTimer?.notes) {
      // Jika sudah ada notes, periksa apakah ada metadata timer
      if (existingTimer.notes.includes("TIMER_STATUS:")) {
        // Jika ada, update metadata timer
        updatedNotes = existingTimer.notes.replace(
          /TIMER_STATUS:.*(\n|$)/,
          `${timerMetadata}\n`
        );
      } else {
        // Jika tidak ada, tambahkan metadata timer
        updatedNotes = `${existingTimer.notes}\n${timerMetadata}`;
      }
    } else {
      // Jika belum ada notes, buat metadata timer baru
      updatedNotes = timerMetadata;
    }

    // Lakukan update rental
    await prisma.rental.update({
      where: { id: rentalId },
      data: {
        notes: updatedNotes
      }
    });
    
    // Revalidasi path terkait dengan kekuatan penuh (force revalidation)
    // Gunakan timestamp untuk memastikan cache tidak menghalangi
    const timestamp = Date.now();
    revalidatePath(`/admin/operations/${rentalId}?t=${timestamp}`);
    revalidatePath(`/user/operations/${rentalId}?t=${timestamp}`);
    revalidatePath(`/admin/operations?t=${timestamp}`);
    revalidatePath(`/user/operations?t=${timestamp}`);
    
    return NextResponse.json({ 
      success: true, 
      data: { rentalId, isPaused, pauseOffset, pauseStartTime },
      timestamp // Tambahkan timestamp untuk membantu client mengetahui waktu terakhir update
    });
  } catch (error) {
    console.error("Timer status update error:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to update timer status" },
      { status: 500 }
    );
  }
}

// API untuk mendapatkan status timer
export async function GET(request: Request) {
  try {
    console.log("GET timer status - received request");
    
    const session = await auth();
    if (!session?.user) {
      console.log("GET timer status - unauthorized user");
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Ambil rental ID dari query parameter
    const url = new URL(request.url);
    const rentalId = url.searchParams.get("rentalId");
    console.log("GET timer status - rentalId:", rentalId);
    
    if (!rentalId) {
      console.log("GET timer status - missing rentalId");
      return NextResponse.json(
        { error: "Rental ID is required" },
        { status: 400 }
      );
    }
    
    // Periksa apakah rental ada
    console.log("GET timer status - finding rental:", rentalId);
    const rental = await prisma.rental.findUnique({
      where: { id: rentalId },
      select: { 
        id: true,
        notes: true
      }
    });
    
    if (!rental) {
      console.log("GET timer status - rental not found:", rentalId);
      return NextResponse.json(
        { error: "Rental not found" },
        { status: 404 }
      );
    }

    // Ambil metadata timer dari notes
    const timerStatus = { 
      rentalId, 
      isPaused: false, 
      pauseOffset: 0, 
      pauseStartTime: 0 
    };
    
    if (rental.notes && rental.notes.includes("TIMER_STATUS:")) {
      // Ekstrak metadata timer
      const timerMetadataMatch = rental.notes.match(/TIMER_STATUS: (.*?)(\n|$)/);
      if (timerMetadataMatch && timerMetadataMatch[1]) {
        const [isPaused, pauseOffset, pauseStartTime] = timerMetadataMatch[1].split(",");
        timerStatus.isPaused = isPaused === "true";
        timerStatus.pauseOffset = parseInt(pauseOffset || "0", 10);
        timerStatus.pauseStartTime = parseInt(pauseStartTime || "0", 10);
      }
    }
    
    console.log("GET timer status - success, returning:", timerStatus);
    // Kurangi cache time untuk memastikan sinkronisasi yang lebih baik
    return new NextResponse(
      JSON.stringify({ 
        success: true, 
        data: timerStatus,
        timestamp: Date.now() // Tambahkan timestamp untuk membantu client melacak segar tidaknya data
      }),
      { 
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      }
    );
  } catch (error) {
    console.error("Get timer status error:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to get timer status" },
      { status: 500 }
    );
  }
} 
