import { TableSkeleton } from "@/app/components/ui/table-skeleton";

export function LoadingState() {
    return (
        <div className="min-h-[400px] flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600"></div>
        </div>
    );
}

interface TableLoadingStateProps {
  rowCount?: number;
}

export function TableLoadingState({ rowCount = 5 }: TableLoadingStateProps) {
  return (
    <div className="w-full">
      <TableSkeleton rowCount={rowCount} variant="violet" />
    </div>
  );
}
