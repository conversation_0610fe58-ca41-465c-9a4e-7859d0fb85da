import { auth } from "@/auth";
import { NextResponse, type NextRequest } from "next/server";
import { LRUCache } from 'lru-cache';
import { getRental } from "@/lib/data/rental";
import { cacheConfig } from './lib/config/metadata';

const WINDOW_SIZE = Number(process.env.RATE_LIMIT_WINDOW) || 60 * 1000; // Pindah ke atas
const MAX_REQUESTS = Number(process.env.RATE_LIMIT_MAX_REQUESTS) || 60;

// Gunakan LRU Cache untuk performa lebih baik
const rateLimit = new LRUCache<string, { count: number; timestamp: number }>({
  max: 500,
  ttl: WINDOW_SIZE,
  updateAgeOnGet: true,
  allowStale: false
});

// Cache whitelist IPs untuk performa lebih baik
const whitelistedIPs = new Set(process.env.WHITELIST_IPS?.split(','));

// Optimize IP whitelist check
const isWhitelisted = (ip: string): boolean => {
  return process.env.NODE_ENV === 'development' || whitelistedIPs.has(ip)
}

function isRateLimited(ip: string): boolean {
  if (isWhitelisted(ip)) {
    return false;
  }

  const now = Date.now();
  const windowStart = now - WINDOW_SIZE;

  const current = rateLimit.get(ip);

  if (!current) {
    rateLimit.set(ip, { count: 1, timestamp: now });
    return false;
  }

  if (current.timestamp < windowStart) {
    rateLimit.set(ip, { count: 1, timestamp: now });
    return false;
  }

  if (current.count >= MAX_REQUESTS) {
    return true;
  }

  current.count++;
  rateLimit.set(ip, current);
  return false;
}

export async function middleware(request: NextRequest) {
  const session = await auth();
  const pathname = request.nextUrl.pathname;
  const ip = request.headers.get("x-forwarded-for") ??
    request.headers.get("x-real-ip") ??
    "unknown";

  if (process.env.NODE_ENV === 'development') {
    console.log(`[${new Date().toISOString()}] ${ip} - ${pathname} - ${session?.user?.email ?? 'anonymous'}`);
  }

  // Redirect dari root ke dashboard jika sudah login
  if (pathname === '/' && session?.user) {
    const redirectUrl = session.user.role === 'ADMIN'
      ? new URL('/admin/dashboard', request.url)
      : new URL('/user/dashboard', request.url);

    // Prevent redirect loops
    if (request.nextUrl.searchParams.get('redirected') !== 'true') {
      redirectUrl.searchParams.set('redirected', 'true');
      return NextResponse.redirect(redirectUrl);
    }
  }

  // Protect admin routes
  if (pathname.startsWith('/admin')) {
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.redirect(new URL('/login', request.url));
    }
  }

  // Protect user routes
  if (pathname.startsWith('/user') && !session?.user) {
    return NextResponse.redirect(new URL('/login', request.url));
  }

  // Redirect authenticated users from auth pages
  if ((pathname.startsWith('/login') || pathname.startsWith('/register')) && session?.user) {
    const redirectUrl = session.user.role === 'ADMIN'
      ? new URL('/admin/dashboard', request.url)
      : new URL('/user/dashboard', request.url);

    // Prevent redirect loops
    if (request.nextUrl.searchParams.get('redirected') !== 'true') {
      redirectUrl.searchParams.set('redirected', 'true');
      return NextResponse.redirect(redirectUrl);
    }
  }

  // Check rate limit untuk API routes
  if (pathname.startsWith('/api/')) {
    if (isRateLimited(ip)) {
      return new NextResponse("Too Many Requests", { status: 429 });
    }
  }

  const productId = pathname.split('/').pop();

  if (productId) {
    const response = NextResponse.next();
    response.headers.set('x-product-id', productId);
    return response;
  }

  // Jika mengakses halaman operasi, cek status pembayaran
  if (pathname.startsWith('/user/operations/')) {
    const rentalId = pathname.split('/')[3];
    const rental = await getRental(rentalId);
    if (rental?.payment?.status?.toLowerCase() === 'DEPOSITED_PENDING') {
      return NextResponse.redirect(new URL('/user/payments/pending/' + rental.id, request.url));
    }
  }

  const response = NextResponse.next();

  // Tambahkan header cache-control berdasarkan jenis aset
  if (pathname.match(/\.(jpg|jpeg|png|webp|avif|gif|svg)$/)) {
    response.headers.set(
      'Cache-Control',
      `public, max-age=${cacheConfig.images.maxAge}, stale-while-revalidate=${cacheConfig.images.staleWhileRevalidate}`
    );
  } else if (pathname.match(/\.(woff|woff2|eot|ttf|otf)$/)) {
    response.headers.set(
      'Cache-Control',
      `public, max-age=${cacheConfig.fonts.maxAge}, stale-while-revalidate=${cacheConfig.fonts.staleWhileRevalidate}`
    );
  } else if (pathname.match(/\.(js|css)$/)) {
    response.headers.set(
      'Cache-Control',
      `public, max-age=${cacheConfig.static.maxAge}, stale-while-revalidate=${cacheConfig.static.staleWhileRevalidate}`
    );
  } else if (pathname.startsWith('/api/')) {
    response.headers.set(
      'Cache-Control',
      `public, max-age=${cacheConfig.api.maxAge}, stale-while-revalidate=${cacheConfig.api.staleWhileRevalidate}`
    );
  }

  // Tambahkan header keamanan
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-XSS-Protection', '1; mode=block');

  return response;
}

export const config = {
  matcher: [
    '/',
    '/admin/:path*',
    '/user/:path*',
    '/login',
    '/register'
  ],
};
