import { Suspense } from 'react';
import { getStats } from '@/lib/data/dashboard';
import { RevenueChart } from "./revenue-chart";
import { DashboardStats } from '@/lib/types/dashboard';

async function StatsCard({ title, value, trend }: { 
  title: string; 
  value: string | number;
  trend?: number;
}) {
  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <h3 className="text-sm font-medium text-gray-500">{title}</h3>
      <p className="mt-2 text-3xl font-semibold text-gray-900">{value}</p>
      {trend !== undefined && (
        <p className={`mt-2 ${trend >= 0 ? 'text-green-600' : 'text-red-600'}`}>
          {trend >= 0 ? '↑' : '↓'} {Math.abs(trend)}%
        </p>
      )}
    </div>
  );
}

export async function StatsOverview() {
  const stats: DashboardStats = await getStats();

  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-4 mb-6">
        <Suspense fallback={<div className="h-32 bg-gray-100 rounded-lg animate-pulse" />}>
          <StatsCard 
            title="Total Pendapatan" 
            value={new Intl.NumberFormat('id-ID', { 
              style: 'currency', 
              currency: 'IDR' 
            }).format(stats.totalRevenue)}
            trend={stats.revenueGrowth}
          />
        </Suspense>
        
        <Suspense fallback={<div className="h-32 bg-gray-100 rounded-lg animate-pulse" />}>
          <StatsCard 
            title="Total Rental" 
            value={stats.totalRentals}
            trend={stats.rentalGrowth}
          />
        </Suspense>
        
        <Suspense fallback={<div className="h-32 bg-gray-100 rounded-lg animate-pulse" />}>
          <StatsCard 
            title="Total Produk" 
            value={stats.totalProducts}
          />
        </Suspense>
        
        <Suspense fallback={<div className="h-32 bg-gray-100 rounded-lg animate-pulse" />}>
          <StatsCard 
            title="Tingkat Okupansi" 
            value={`${stats.occupancyRate}%`}
            trend={stats.occupancyGrowth}
          />
        </Suspense>
      </div>
      <RevenueChart data={stats.revenueData} />
    </div>
  );
}
