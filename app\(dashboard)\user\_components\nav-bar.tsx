'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
    LuLayoutDashboard as LuH<PERSON>,
    LuBox,
    LuClock,
    LuUser
} from 'react-icons/lu';
import { MenuItem } from '@/lib/types/menu';

const menuItems: MenuItem[] = [
    {
        href: '/user/dashboard',
        title: '<PERSON>randa',
        icon: LuHome
    },
    {
        href: '/user/catalog',
        title: 'Katalog',
        icon: <PERSON><PERSON><PERSON>
    },
    {
        href: '/user/rentals',
        title: 'Rental Saya',
        icon: <PERSON><PERSON><PERSON>
    },
    {
        href: '/user/profile',
        title: 'Profil',
        icon: LuUser
    }
];

export function Navbar() {
    const pathname = usePathname();

    return (
        <nav className="hidden md:flex space-x-4">
            {menuItems.map((item) => {
                const Icon = item.icon;
                return (
                    <Link
                        key={item.href}
                        href={item.href}
                        className={`flex items-center px-3 py-2 rounded-md text-sm font-medium ${pathname === item.href
                            ? 'bg-blue-50 text-blue-600'
                            : 'text-gray-600 hover:bg-gray-50'
                            }`}
                    >
                        <Icon className="w-4 h-4 mr-2" />
                        {item.title}
                    </Link>
                );
            })}
        </nav>
    );
} 
