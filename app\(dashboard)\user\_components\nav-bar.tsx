'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
    LuLayoutDashboard as LuH<PERSON>,
    LuBox,
    LuClock,
    LuUser
} from 'react-icons/lu';
import { MenuItem } from '@/lib/types/menu';
import { cn } from "@/lib/utils/cn";

const menuItems: MenuItem[] = [
    {
        href: '/user/dashboard',
        title: '<PERSON><PERSON><PERSON>',
        icon: LuHome
    },
    {
        href: '/user/catalog',
        title: 'Katalog',
        icon: LuB<PERSON>
    },
    {
        href: '/user/rentals',
        title: 'Rental Saya',
        icon: LuC<PERSON>
    },
    {
        href: '/user/profile',
        title: 'Profil',
        icon: LuUser
    }
];

export function Navbar() {
    const pathname = usePathname();

    return (
        <nav className="hidden md:flex items-center space-x-2" role="navigation">
            {menuItems.map((item) => {
                const Icon = item.icon;
                const isActive = pathname === item.href;

                return (
                    <Link
                        key={item.href}
                        href={item.href}
                        className={cn(
                            "group relative flex items-center min-h-[44px] px-4 py-2.5 rounded-xl text-sm font-medium transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98]",
                            "focus:outline-none focus:ring-2 focus:ring-violet-500 focus:ring-offset-2",
                            "shadow-sm hover:shadow-md",
                            isActive
                                ? "bg-gradient-to-r from-violet-600 to-purple-600 text-white shadow-lg hover:shadow-xl"
                                : "bg-white/80 text-violet-700 hover:bg-violet-50 hover:text-violet-800 border border-violet-100 hover:border-violet-200 dark:bg-gray-800/80 dark:text-violet-300 dark:hover:bg-violet-900/20 dark:hover:text-violet-200 dark:border-violet-800 dark:hover:border-violet-700"
                        )}
                    >
                        <Icon className={cn(
                            "w-4 h-4 mr-2.5 transition-all duration-200",
                            "group-hover:scale-110",
                            isActive
                                ? "text-white"
                                : "text-violet-600 group-hover:text-violet-700 dark:text-violet-400 dark:group-hover:text-violet-300"
                        )} />
                        <span className="relative">
                            {item.title}
                            {isActive && (
                                <div className="absolute -bottom-1 left-0 right-0 h-0.5 bg-white/50 rounded-full" />
                            )}
                        </span>

                        {/* Active indicator dot */}
                        {isActive && (
                            <div className="absolute -top-1 -right-1 w-2 h-2 bg-white rounded-full animate-pulse shadow-sm" />
                        )}

                        {/* Hover effect background */}
                        <div className={cn(
                            "absolute inset-0 rounded-xl opacity-0 transition-opacity duration-200",
                            "bg-gradient-to-r from-violet-500/10 to-purple-500/10",
                            "group-hover:opacity-100"
                        )} />
                    </Link>
                );
            })}
        </nav>
    );
}
