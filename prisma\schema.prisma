// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["metrics"]
}

datasource db {
  provider     = "postgresql"
  url          = env("DATABASE_URL")
  relationMode = "prisma"
  directUrl    = env("DATABASE_URL_UNPOOLED")
}

model Account {
  id                String  @id @default(cuid())
  userId            String  @map("user_id")
  type              String
  provider          String
  providerAccountId String  @map("provider_account_id")
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@index([userId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique @map("session_token")
  userId       String   @map("user_id")
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@map("sessions")
}

model User {
  id            String                 @id @default(cuid())
  name          String?
  email         String                 @unique
  emailVerified DateTime?              @map("email_verified")
  image         String?
  password      String?
  phone         String?
  role          UserRole               @default(USER)
  createdAt     DateTime               @default(now())
  updatedAt     DateTime               @updatedAt
  accounts      Account[]
  sessions      Session[]
  products      Product[]
  rentals       Rental[]
  reviews       Review[]
  maintenances  Maintenance[]
  statusHistory PaymentStatusHistory[]
  notifications Notification[]
  Payment       Payment[]

  @@map("users")
}

enum UserRole {
  USER
  ADMIN
}

enum ProductStatus {
  AVAILABLE
  NOT_AVAILABLE
  MAINTENANCE
}

model Product {
  id           String        @id @default(cuid())
  name         String
  price        Float
  description  String?
  image        String?
  imageUrl     String?
  capacity     Int
  stock        Int           @default(0)
  status       ProductStatus @default(AVAILABLE)
  userId       String        @map("user_id")
  category     String?
  overtimeRate Float? // Tarif overtime per jam (opsional)
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
  user         User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  rentals      Rental[]
  reviews      Review[]
  maintenances Maintenance[]

  @@index([status])
  @@index([userId])
}

model Rental {
  id               String       @id @default(cuid())
  userId           String       @map("user_id")
  productId        String       @map("product_id")
  startDate        DateTime     @map("start_date")
  endDate          DateTime     @map("end_date")
  arrivalTime      String       @map("arrival_time")
  quantity         Int          @default(1)
  location         String
  address          String?
  notes            String?
  purpose          String
  duration         String?
  status           RentalStatus @default(PENDING)
  paymentStatus    String       @default("pending")
  amount           Float        @default(0)
  createdAt        DateTime     @default(now()) @map("created_at")
  updatedAt        DateTime     @updatedAt @map("updated_at")
  user             User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  product          Product      @relation(fields: [productId], references: [id], onDelete: Cascade)
  payment          Payment?
  review           Review?
  operationalStart DateTime?
  operationalEnd   DateTime?
  overtime         Float?       @default(0)

  @@index([userId])
  @@index([productId])
  @@map("rentals")
}

enum RentalStatus {
  PENDING
  CONFIRMED
  ACTIVE
  COMPLETED
  CANCELLED
}

model Payment {
  id                   String                 @id @default(cuid())
  rentalId             String                 @unique
  rental               Rental                 @relation(fields: [rentalId], references: [id])
  amount               Float
  deposit              Float
  remaining            Float                  @default(0)
  overtime             Float?
  status               PaymentStatus          @default(DEPOSIT_PENDING)
  transactionId        String?
  snapToken            String?
  createdAt            DateTime               @default(now())
  updatedAt            DateTime               @updatedAt
  User                 User?                  @relation(fields: [userId], references: [id])
  userId               String?
  PaymentStatusHistory PaymentStatusHistory[]

  @@index([rentalId])
  @@index([userId])
}

enum PaymentStatus {
  DEPOSIT_PENDING
  DEPOSIT_PAID
  FULLY_PAID
  FAILED
  INVOICE_ISSUED
}

model PaymentStatusHistory {
  id        String        @id @default(cuid())
  paymentId String        @map("payment_id")
  userId    String        @map("user_id")
  newStatus PaymentStatus
  createdAt DateTime      @default(now())
  payment   Payment       @relation(fields: [paymentId], references: [id], onDelete: Cascade)
  user      User          @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([paymentId])
  @@index([userId])
  @@map("payment_status_history")
}

model Review {
  id        String   @id @default(cuid())
  productId String   @map("product_id")
  userId    String   @map("user_id")
  rentalId  String   @unique @map("rental_id")
  rating    Int
  comment   String?
  createdAt DateTime @default(now())
  product   Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  rental    Rental   @relation(fields: [rentalId], references: [id], onDelete: Cascade)

  @@index([productId])
  @@index([userId])
}

model Maintenance {
  id        String          @id @default(cuid())
  productId String          @map("product_id")
  userId    String?
  date      DateTime
  type      MaintenanceType
  notes     String?
  cost      Int?
  createdAt DateTime        @default(now())
  product   Product         @relation(fields: [productId], references: [id], onDelete: Cascade)
  user      User?           @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@index([productId])
  @@index([userId])
}

enum MaintenanceType {
  ROUTINE
  REPAIR
  EMERGENCY
}

model RateLimit {
  id        String   @id @default(cuid())
  ip        String
  timestamp DateTime @default(now())

  @@index([ip, timestamp])
  @@map("rate_limits")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

model Notification {
  id        String           @id @default(cuid())
  userId    String
  title     String
  message   String
  type      NotificationType
  isRead    Boolean          @default(false)
  createdAt DateTime         @default(now())
  user      User             @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@map("notifications")
}

enum NotificationType {
  PAYMENT_SUCCESS
  PAYMENT_FAILED
  NEW_RENTAL
  RENTAL_CONFIRMED
  OPERATION_STARTED
  OPERATION_COMPLETED
  LOW_STOCK
  OVERTIME_DETECTED
  NEW_PAYMENT
  NEW_INVOICE
}
