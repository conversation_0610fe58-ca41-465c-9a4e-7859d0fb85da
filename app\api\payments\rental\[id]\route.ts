import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import { prisma } from "@/lib/config/prisma";

export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const rentalId = params.id;
    
    if (!rentalId) {
      return NextResponse.json(
        { error: "ID penyewaan tidak ditemukan" },
        { status: 400 }
      );
    }

    // Cari rental dan pastikan pengguna adalah pemilik rental atau admin
    const rental = await prisma.rental.findUnique({
      where: { id: rentalId },
      include: {
        product: {
          select: {
            name: true,
            capacity: true,
            price: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
          },
        },
        payment: true,
      },
    });

    if (!rental) {
      return NextResponse.json(
        { error: "Data penyewaan tidak ditemukan" },
        { status: 404 }
      );
    }

    // Validasi akses - hanya pemilik rental atau admin yang bisa melihat
    const isAdmin = session.user.role === "ADMIN";
    const isOwner = rental.userId === session.user.id;

    if (!isAdmin && !isOwner) {
      return NextResponse.json(
        { error: "Anda tidak memiliki akses" },
        { status: 403 }
      );
    }

    // Hitung overtime jika operasi sudah selesai
    let overtime = 0;
    if (rental.operationalEnd && rental.operationalStart) {
      // Menggunakan durasi dari rental jika tersedia
      const durationStr = rental.duration || "1x8_HOURS";
      // Ambil durasi dalam hari (misalnya: 1 untuk durasi 1x8_HOURS)
      const bookedDurationInDays = parseInt(durationStr.split('x')[0]) || 1;
      
      const actualDurationInDays = Math.ceil(
        (rental.operationalEnd.getTime() - rental.operationalStart.getTime()) /
          (1000 * 60 * 60 * 24)
      );
      
      // Jika durasi aktual lebih lama dari yang dipesan
      if (actualDurationInDays > bookedDurationInDays) {
        const overtimeDays = actualDurationInDays - bookedDurationInDays;
        // Tarif overtime: 110% dari harga normal
        const overtimeRate = rental.product.price * 1.1;
        overtime = overtimeDays * overtimeRate;
      }
    }

    // Ambil data pembayaran
    const payment = rental.payment;
    
    if (!payment) {
      return NextResponse.json(
        { error: "Data pembayaran tidak ditemukan" },
        { status: 404 }
      );
    }

    // Format respons
    const paymentData = {
      id: payment.id,
      rentalId: rental.id,
      deposit: payment.deposit,
      remaining: payment.remaining,
      overtime: overtime > 0 ? overtime : undefined,
      status: payment.status,
      createdAt: payment.createdAt,
      updatedAt: payment.updatedAt,
      rental: {
        id: rental.id,
        operationalStart: rental.operationalStart,
        operationalEnd: rental.operationalEnd,
        duration: rental.duration,
        startDate: rental.startDate,
        status: rental.status,
        product: {
          name: rental.product.name,
          capacity: rental.product.capacity,
          price: rental.product.price,
        },
        user: {
          id: rental.user.id,
          name: rental.user.name,
        },
      },
    };

    return NextResponse.json(paymentData);
  } catch (error) {
    console.error("Error fetching payment data:", error);
    return NextResponse.json(
      { error: "Gagal mengambil data pembayaran" },
      { status: 500 }
    );
  }
} 