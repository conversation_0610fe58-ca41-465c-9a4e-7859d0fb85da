"use client";

import { useEffect, useState } from "react";
import {  useRouter, useSearchParams } from "next/navigation";
import { PaymentSuccess } from "@/app/components/payment/payment-success";

export default function PaymentSuccessPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [orderDetails, setOrderDetails] = useState({
    id: "",
    productName: "",
    totalAmount: 0,
    depositAmount: 0,
    remainingAmount: 0,
    paymentType: "deposit" as "deposit" | "remaining" | "full"
  });
  const [loading, setLoading] = useState(true);
  const [dataFound, setDataFound] = useState(true);

  useEffect(() => {
    async function loadPaymentDetails() {
      // Get rental ID from search params
      const rentalId = searchParams.get('rentalId');
      const type = searchParams.get('type') as "deposit" | "remaining" | "full";

      if (!rentalId) {
        console.log("Tidak ada rental ID yang ditemukan");
        setDataFound(false);
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        console.log("Mencoba mengambil data rental dengan ID:", rentalId);

        // Ambil data rental
        const response = await fetch(`/api/rentals/${rentalId}`, {
          cache: 'no-store',
          headers: {
            'Content-Type': 'application/json'
          }
        });

        // Jika user tidak terautentikasi, arahkan ke halaman login
        if (response.status === 401) {
          console.error("User tidak terautentikasi");
          router.push('/login?redirect=' + encodeURIComponent('/user/payments/success?rentalId=' + rentalId));
          return;
        }

        if (!response.ok) {
          console.log(`Data tidak ditemukan dengan ID: ${rentalId}`);
          setDataFound(false);
          setLoading(false);
          return;
        }

        const data = await response.json();

        // Jika data tidak valid - lebih toleran untuk payment yang mungkin null
        if (!data) {
          setDataFound(false);
          setLoading(false);
          return;
        }

        // Jika tidak ada payment data, buat default payment data
        if (!data.payment) {
          data.payment = {
            deposit: Math.floor(data.amount * 0.5),
            remaining: Math.ceil(data.amount * 0.5),
            status: 'DEPOSIT_PENDING'
          };
        }

        // Set order details
        setOrderDetails({
          id: data.id || "",
          productName: data.product?.name || "Produk tidak diketahui",
          totalAmount: data.amount || 0,
          depositAmount: data.payment?.deposit || 0,
          remainingAmount: data.payment?.remaining || 0,
          paymentType: type || "deposit"
        });

        setDataFound(true);
      } catch (error) {
        console.error('Error loading payment details:', error);
        setDataFound(false);
      } finally {
        setLoading(false);
      }
    }

    loadPaymentDetails();
  }, [searchParams, router]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <p className="text-muted-foreground">Memuat...</p>
      </div>
    );
  }

  if (!dataFound) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-6 max-w-md w-full text-center">
          <p className="text-red-600 dark:text-red-400 mb-4">Detail pembayaran tidak ditemukan.</p>
          <button
            onClick={() => router.push('/user/dashboard')}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Kembali ke Dashboard
          </button>
        </div>
      </div>
    );
  }

  return <PaymentSuccess orderDetails={orderDetails} />;
}
