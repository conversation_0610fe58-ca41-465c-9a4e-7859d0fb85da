'use client';

import { useTheme } from 'next-themes';
import { useEffect, useState } from 'react';

interface UserHeaderGradientProps {
  children: React.ReactNode;
  className?: string;
}

export function UserHeaderGradient({ children, className = '' }: UserHeaderGradientProps) {
  const { theme, systemTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Always show gradient, even during SSR
  const currentTheme = theme === 'system' ? systemTheme : theme;
  const isDark = mounted && currentTheme === 'dark';

  // Use a more direct approach with inline styles
  const inlineStyle = {
    background: isDark
      ? 'linear-gradient(to right, #4c1d95, #581c87, #3730a3)'
      : 'linear-gradient(to right, #7c3aed, #9333ea, #6366f1)',
    backgroundImage: isDark
      ? 'linear-gradient(to right, #4c1d95, #581c87, #3730a3)'
      : 'linear-gradient(to right, #7c3aed, #9333ea, #6366f1)',
    backgroundColor: isDark ? '#4c1d95' : '#7c3aed',
    backgroundAttachment: 'scroll',
    backgroundClip: 'border-box',
    backgroundOrigin: 'padding-box',
    backgroundPosition: '0% 0%',
    backgroundRepeat: 'no-repeat',
    backgroundSize: 'auto'
  };

  return (
    <div
      className={className}
      style={inlineStyle}
      data-gradient="true"
    >
      {children}
    </div>
  );
}
