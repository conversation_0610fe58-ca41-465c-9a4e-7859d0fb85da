"use client";

import { cn } from "@/lib/utils";
import {
  DesktopNavigationSkeleton,
  TabletNavigationSkeleton,
  MobileNavigationSkeleton,
  MobileBottomNavigationSkeleton,
  MobileMenuPanelSkeleton
} from "./navigation-skeleton";
import { NavigationLoadingShimmer } from "./layout-loading";

export function SkeletonSizeDemo() {
  return (
    <div className="p-8 space-y-12 bg-gray-50 dark:bg-gray-900 min-h-screen">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
          Navigation Skeleton Size Demo
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mb-8">
          Demonstrating realistic skeleton sizes with "Rental Saya" being visibly longer
        </p>

        {/* Desktop Skeleton Demo */}
        <section className="mb-12">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
            Desktop Navigation (≥1024px)
          </h2>
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <DesktopNavigationSkeleton />
            
            {/* Size Reference */}
            <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
              <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-4">
                Size Reference (Desktop):
              </h3>
              <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                <div className="flex items-center justify-between">
                  <span>Dashboard:</span>
                  <span className="font-mono">w-20 (80px)</span>
                </div>
                <div className="flex items-center justify-between">
                  <span>Katalog:</span>
                  <span className="font-mono">w-16 (64px)</span>
                </div>
                <div className="flex items-center justify-between bg-violet-50 dark:bg-violet-900/20 px-2 py-1 rounded">
                  <span className="font-bold text-violet-700 dark:text-violet-300">Rental Saya ⭐:</span>
                  <span className="font-mono font-bold text-violet-700 dark:text-violet-300">w-28 (112px) - LONGEST</span>
                </div>
                <div className="flex items-center justify-between">
                  <span>Status Operasi:</span>
                  <span className="font-mono">w-32 (128px)</span>
                </div>
                <div className="flex items-center justify-between">
                  <span>Pembayaran:</span>
                  <span className="font-mono">w-24 (96px)</span>
                </div>
                <div className="flex items-center justify-between">
                  <span>Profil:</span>
                  <span className="font-mono">w-16 (64px)</span>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Tablet Skeleton Demo */}
        <section className="mb-12">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
            Tablet Navigation (769px-1023px)
          </h2>
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <TabletNavigationSkeleton />
            
            <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
              <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-4">
                Size Reference (Tablet):
              </h3>
              <div className="grid grid-cols-2 gap-4 text-sm text-gray-600 dark:text-gray-400">
                <div className="flex justify-between">
                  <span>Dashboard:</span>
                  <span className="font-mono">w-18 (72px)</span>
                </div>
                <div className="flex justify-between">
                  <span>Katalog:</span>
                  <span className="font-mono">w-14 (56px)</span>
                </div>
                <div className="flex justify-between bg-violet-50 dark:bg-violet-900/20 px-2 py-1 rounded col-span-2">
                  <span className="font-bold text-violet-700 dark:text-violet-300">Rental Saya ⭐:</span>
                  <span className="font-mono font-bold text-violet-700 dark:text-violet-300">w-24 (96px) - LONGEST</span>
                </div>
                <div className="flex justify-between">
                  <span>Profil:</span>
                  <span className="font-mono">w-14 (56px)</span>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Mobile Skeleton Demo */}
        <section className="mb-12">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
            Mobile Navigation (≤768px)
          </h2>
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            {/* Mobile Header */}
            <div className="mb-6">
              <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-4">
                Mobile Header:
              </h3>
              <MobileNavigationSkeleton />
            </div>

            {/* Mobile Bottom Navigation */}
            <div className="mb-6">
              <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-4">
                Mobile Bottom Navigation:
              </h3>
              <div className="relative">
                <MobileBottomNavigationSkeleton />
                <div className="h-20"></div> {/* Spacer */}
              </div>
            </div>

            {/* Mobile Menu Panel */}
            <div className="mb-6">
              <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-4">
                Mobile Menu Panel:
              </h3>
              <div className="relative h-96 overflow-hidden rounded-lg border border-gray-200 dark:border-gray-700">
                <MobileMenuPanelSkeleton />
              </div>
            </div>

            <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
              <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-4">
                Size Reference (Mobile Menu):
              </h3>
              <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                <div className="flex justify-between">
                  <span>Beranda:</span>
                  <span className="font-mono">w-16 (64px)</span>
                </div>
                <div className="flex justify-between">
                  <span>Katalog:</span>
                  <span className="font-mono">w-14 (56px)</span>
                </div>
                <div className="flex justify-between bg-violet-50 dark:bg-violet-900/20 px-2 py-1 rounded">
                  <span className="font-bold text-violet-700 dark:text-violet-300">Rental Saya ⭐:</span>
                  <span className="font-mono font-bold text-violet-700 dark:text-violet-300">w-20 (80px) - LONGEST</span>
                </div>
                <div className="flex justify-between">
                  <span>Profil:</span>
                  <span className="font-mono">w-12 (48px)</span>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Shimmer Effect Demo */}
        <section className="mb-12">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
            Shimmer Effect Demo
          </h2>
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <NavigationLoadingShimmer />
          </div>
        </section>

        {/* Size Comparison Chart */}
        <section className="mb-12">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
            Complete Size Comparison
          </h2>
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b border-gray-200 dark:border-gray-700">
                    <th className="text-left py-3 text-gray-600 dark:text-gray-400">Navigation Item</th>
                    <th className="text-center py-3 text-gray-600 dark:text-gray-400">Desktop</th>
                    <th className="text-center py-3 text-gray-600 dark:text-gray-400">Tablet</th>
                    <th className="text-center py-3 text-gray-600 dark:text-gray-400">Mobile</th>
                    <th className="text-center py-3 text-gray-600 dark:text-gray-400">Characters</th>
                  </tr>
                </thead>
                <tbody className="text-gray-700 dark:text-gray-300">
                  <tr className="border-b border-gray-100 dark:border-gray-700">
                    <td className="py-3">Dashboard</td>
                    <td className="text-center py-3 font-mono">w-20 (80px)</td>
                    <td className="text-center py-3 font-mono">w-18 (72px)</td>
                    <td className="text-center py-3 font-mono">w-16 (64px)</td>
                    <td className="text-center py-3">9</td>
                  </tr>
                  <tr className="border-b border-gray-100 dark:border-gray-700">
                    <td className="py-3">Katalog</td>
                    <td className="text-center py-3 font-mono">w-16 (64px)</td>
                    <td className="text-center py-3 font-mono">w-14 (56px)</td>
                    <td className="text-center py-3 font-mono">w-14 (56px)</td>
                    <td className="text-center py-3">7</td>
                  </tr>
                  <tr className="border-b border-gray-100 dark:border-gray-700 bg-violet-50 dark:bg-violet-900/20">
                    <td className="py-3 font-bold text-violet-600 dark:text-violet-400">Rental Saya ⭐</td>
                    <td className="text-center py-3 font-mono font-bold text-violet-600 dark:text-violet-400">w-28 (112px)</td>
                    <td className="text-center py-3 font-mono font-bold text-violet-600 dark:text-violet-400">w-24 (96px)</td>
                    <td className="text-center py-3 font-mono font-bold text-violet-600 dark:text-violet-400">w-20 (80px)</td>
                    <td className="text-center py-3 font-bold text-violet-600 dark:text-violet-400">11</td>
                  </tr>
                  <tr className="border-b border-gray-100 dark:border-gray-700">
                    <td className="py-3">Profil</td>
                    <td className="text-center py-3 font-mono">w-16 (64px)</td>
                    <td className="text-center py-3 font-mono">w-14 (56px)</td>
                    <td className="text-center py-3 font-mono">w-12 (48px)</td>
                    <td className="text-center py-3">6</td>
                  </tr>
                </tbody>
              </table>
            </div>
            
            <div className="mt-6 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-700">
              <h4 className="text-sm font-medium text-green-800 dark:text-green-300 mb-2">
                ✅ Size Differentiation Achieved!
              </h4>
              <p className="text-sm text-green-700 dark:text-green-400">
                "Rental Saya" skeleton is visibly longer than other navigation items across all breakpoints, 
                providing accurate preview of the final layout.
              </p>
            </div>
          </div>
        </section>

        {/* Testing Instructions */}
        <section className="mb-12">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
            How to Test Skeleton Loading
          </h2>
          <div className="bg-blue-50 dark:bg-blue-900/20 rounded-xl p-6 border border-blue-200 dark:border-blue-700">
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">1</div>
                <div>
                  <h3 className="font-medium text-blue-800 dark:text-blue-300">Navigate to Dashboard</h3>
                  <p className="text-sm text-blue-700 dark:text-blue-400">
                    Go to <code className="bg-blue-100 dark:bg-blue-900/50 px-1 rounded">/user</code> and refresh the page (F5)
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">2</div>
                <div>
                  <h3 className="font-medium text-blue-800 dark:text-blue-300">Observe Sidebar Skeleton</h3>
                  <p className="text-sm text-blue-700 dark:text-blue-400">
                    Watch the sidebar navigation skeleton - "Rental Saya" should be visibly longer than other items
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">3</div>
                <div>
                  <h3 className="font-medium text-blue-800 dark:text-blue-300">Test Responsive Behavior</h3>
                  <p className="text-sm text-blue-700 dark:text-blue-400">
                    Use browser dev tools to test different screen sizes and see how skeleton adapts
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
}
