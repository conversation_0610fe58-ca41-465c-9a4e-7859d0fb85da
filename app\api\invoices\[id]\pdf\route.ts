import { NextResponse } from "next/server";
import { auth } from "@/auth";
import { prisma } from "@/lib/config/prisma";
import { formatCurrency, formatDate } from "@/lib/utils/format";

// Definisikan interface untuk property tambahan
interface RentalWithAdditionalFields {
  location?: string;
  contactPhone?: string;
}

export async function GET(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const userId = session.user.id;
    const { id } = await params;

    // Dapatkan data invoice dengan alamat lengkap
    const invoice = await prisma.rental.findUnique({
      where: { id },
      include: {
        user: true,
        product: true,
        payment: true
      }
    });

    if (!invoice || invoice.userId !== userId) {
      return NextResponse.json(
        { error: "Invoice not found" },
        { status: 404 }
      );
    }

    // Casting invoice ke type yang memiliki field tambahan
    const rentalData = invoice as unknown as RentalWithAdditionalFields;

    // Hitung deposit dan sisa pembayaran
    const depositAmount = Math.floor(invoice.amount * 0.5);
    const remainingAmount = invoice.amount - depositAmount;

    // Overtime kalau ada
    const overtimeAmount = invoice.payment?.overtime || 0;

    // Total dengan overtime
    const totalWithOvertime = invoice.amount + overtimeAmount;

    // Buat template HTML untuk invoice yang bisa diprint
    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Invoice #${id.substring(0, 8)}</title>
          <style>
            @page {
              size: A4;
              margin: 0;
            }
            @media print {
              body {
                margin: 0;
                color: #000;
                background-color: #fff;
                background: none;
              }
              .container {
                box-shadow: none;
                border-radius: 0;
                background: white;
              }
              .invoice-box {
                box-shadow: none;
                border: 0;
              }
              .download-section {
                display: none;
              }
              .invoice-header {
                background: none !important;
                color: #000 !important;
                border: 2px solid #000;
              }
              .company-logo, .invoice-title, .invoice-number, .invoice-date {
                color: #000 !important;
              }
            }
            body {
              font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              margin: 0;
              padding: 20px;
              color: #1f2937;
              font-size: 13px;
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              min-height: 100vh;
            }
            .container {
              max-width: 900px;
              margin: 0 auto;
              background: white;
              padding: 0;
              border-radius: 16px;
              box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
              overflow: hidden;
            }

            .invoice-box {
              max-width: 100%;
              margin: 0;
              padding: 40px;
              border: none;
              background-color: #fff;
            }
            .invoice-header {
              text-align: center;
              margin-bottom: 20px;
              padding: 16px;
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              color: white;
              border-radius: 10px;
              margin: -40px -40px 20px -40px;
            }
            .company-logo {
              text-align: center;
              font-size: 20px;
              font-weight: 700;
              color: white;
              margin-bottom: 6px;
              letter-spacing: 0.5px;
            }
            .invoice-title {
              font-size: 24px;
              font-weight: 600;
              margin: 6px 0;
              color: white;
              text-shadow: 0 1px 2px rgba(0,0,0,0.1);
            }
            .invoice-number {
              font-size: 14px;
              color: rgba(255,255,255,0.9);
              margin-bottom: 4px;
              font-weight: 500;
            }
            .invoice-date {
              color: rgba(255,255,255,0.8);
              font-size: 12px;
            }
            .rental-info {
              margin-bottom: 20px;
              background: #f8fafc;
              padding: 16px;
              border-radius: 8px;
              border: 1px solid #e2e8f0;
            }
            .section-title {
              font-size: 16px;
              font-weight: 700;
              margin-bottom: 12px;
              color: #1e293b;
              padding-bottom: 6px;
              border-bottom: 2px solid #667eea;
              display: inline-block;
            }
            table {
              width: 100%;
              border-collapse: collapse;
              margin: 12px 0;
              background: white;
              border-radius: 6px;
              overflow: hidden;
              box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            }
            table th, table td {
              padding: 12px;
              text-align: left;
              border-bottom: 1px solid #f1f5f9;
              font-size: 13px;
            }
            table th {
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              color: white;
              font-weight: 600;
              font-size: 14px;
              text-transform: uppercase;
              letter-spacing: 0.5px;
            }
            .text-right {
              text-align: right;
            }
            .total-row {
              font-weight: 700;
              font-size: 16px;
              background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
              color: #1e293b;
            }
            .payment-info {
              margin: 20px 0;
              padding: 16px;
              background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
              border: 2px solid #0ea5e9;
              border-radius: 8px;
              box-shadow: 0 2px 4px rgba(14, 165, 233, 0.1);
            }
            .payment-schedule {
              display: flex;
              justify-content: space-between;
              margin: 8px 0;
              padding: 8px 12px;
              background: rgba(255,255,255,0.7);
              border-radius: 6px;
              font-weight: 600;
              font-size: 13px;
            }
            .footer {
              margin-top: 20px;
              text-align: center;
              font-size: 12px;
              color: #64748b;
              border-top: 1px solid #e2e8f0;
              padding-top: 16px;
              background: #f8fafc;
              margin-left: -40px;
              margin-right: -40px;
              margin-bottom: -40px;
              padding-left: 40px;
              padding-right: 40px;
              padding-bottom: 20px;
            }
            .status-paid {
              color: #10b981;
              font-weight: 800;
              border: 3px solid #10b981;
              display: inline-block;
              padding: 8px 16px;
              transform: rotate(-15deg);
              position: absolute;
              top: 20px;
              right: 20px;
              font-size: 18px;
              opacity: 0.9;
              background: rgba(16, 185, 129, 0.1);
              border-radius: 8px;
              backdrop-filter: blur(10px);
            }
            .status-wrapper {
              position: relative;
            }
            .status-pending {
              color: #f59e0b;
              font-weight: 800;
              border: 3px solid #f59e0b;
              display: inline-block;
              padding: 8px 16px;
              transform: rotate(-15deg);
              position: absolute;
              top: 20px;
              right: 20px;
              font-size: 18px;
              opacity: 0.9;
              background: rgba(245, 158, 11, 0.1);
              border-radius: 8px;
              backdrop-filter: blur(10px);
            }
            .download-section {
              text-align: center;
              margin: 30px 0;
              padding: 24px;
              background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
              border-radius: 16px;
              border: 2px solid #e2e8f0;
            }
            .download-button {
              display: inline-flex;
              align-items: center;
              gap: 8px;
              padding: 12px 24px;
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              color: white;
              border: none;
              border-radius: 12px;
              font-size: 16px;
              font-weight: 600;
              cursor: pointer;
              transition: all 0.3s ease;
              box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
              text-decoration: none;
              margin: 0 8px;
            }
            .download-button:hover {
              transform: translateY(-2px);
              box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
            }
            .download-button.print-btn {
              background: linear-gradient(135deg, #10b981 0%, #059669 100%);
              box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
            }
            .download-button.print-btn:hover {
              box-shadow: 0 8px 25px rgba(16, 185, 129, 0.6);
            }
            .download-icon {
              width: 18px;
              height: 18px;
              fill: currentColor;
            }
          </style>
          <script>
            function printInvoice() {
              window.print();
            }

            function downloadPDF() {
              // Trigger browser's save dialog
              const filename = 'Invoice-${id.substring(0, 8).toUpperCase()}.pdf';
              window.print();
            }
          </script>
        </head>
        <body>
          <div class="container">
            <!-- Download Section -->
            <div class="download-section">
              <h3 style="margin: 0 0 16px 0; color: #1e293b; font-size: 18px; font-weight: 600;">📄 Invoice #${id.substring(0, 8).toUpperCase()}</h3>
              <p style="margin: 0 0 20px 0; color: #64748b; font-size: 14px;">Klik tombol di bawah untuk mencetak atau menyimpan invoice ini</p>
              <div style="display: flex; justify-content: center; gap: 12px; flex-wrap: wrap;">
                <button onclick="printInvoice()" class="download-button print-btn">
                  <svg class="download-icon" viewBox="0 0 24 24">
                    <path d="M17 17H7V14H5v5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-5h-2v3zM10 10.5h4l-2-2-2 2zM12 2a1 1 0 0 0-1 1v8.5l2-2V3a1 1 0 0 0-1-1z"/>
                  </svg>
                  Cetak Invoice
                </button>
                <button onclick="downloadPDF()" class="download-button">
                  <svg class="download-icon" viewBox="0 0 24 24">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6z"/>
                    <polyline points="14,2 14,8 20,8"/>
                    <line x1="16" y1="13" x2="8" y2="13"/>
                    <line x1="16" y1="17" x2="8" y2="17"/>
                    <polyline points="10,9 9,9 8,9"/>
                  </svg>
                  Download PDF
                </button>
              </div>
            </div>

            <div class="invoice-box">
              <div class="status-wrapper">
                ${invoice.payment?.status === "FULLY_PAID" ?
                  '<div class="status-paid">LUNAS</div>' :
                  invoice.payment?.status === "DEPOSIT_PAID" ?
                  '<div class="status-pending">DEPOSIT DIBAYAR</div>' :
                  '<div class="status-pending">MENUNGGU PEMBAYARAN</div>'
                }
              </div>

              <div class="invoice-header">
                <div class="company-logo">RENTAL GENSET</div>
                <div class="invoice-title">INVOICE</div>
                <div class="invoice-number">#${id.substring(0, 8).toUpperCase()}</div>
                <div class="invoice-date">Tanggal: ${formatDate(invoice.createdAt)}</div>
              </div>



              <div class="rental-info">
                <div class="section-title">Informasi Sewa</div>
                <table>
                  <tr>
                    <td style="width: 30%"><strong>Pelanggan</strong></td>
                    <td>${invoice.user.name} (${invoice.user.phone || 'No telp tidak tersedia'})</td>
                  </tr>
                  <tr>
                    <td><strong>Produk</strong></td>
                    <td>${invoice.product.name} - ${invoice.product.capacity} kVA</td>
                  </tr>
                  <tr>
                    <td><strong>Lokasi Operasi</strong></td>
                    <td>${invoice.address || 'Tidak tersedia'}</td>
                  </tr>
                  <tr>
                    <td><strong>Periode Sewa</strong></td>
                    <td>${formatDate(invoice.startDate)} - ${formatDate(invoice.endDate)}</td>
                  </tr>
                </table>
              </div>

              <div class="section-title">Rincian Biaya</div>
              <table>
                <thead>
                  <tr>
                    <th style="width: 60%">Deskripsi</th>
                    <th class="text-right">Jumlah</th>
                    <th class="text-right">Harga</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>
                      ${invoice.product.name} (${invoice.product.capacity} kVA)<br>
                      <small>Periode: ${formatDate(invoice.startDate)} - ${formatDate(invoice.endDate)}</small>
                    </td>
                    <td class="text-right">1</td>
                    <td class="text-right">${formatCurrency(invoice.amount)}</td>
                  </tr>
                  ${overtimeAmount > 0 ? `
                  <tr>
                    <td>
                      Biaya Overtime<br>
                      <small>Penggunaan melebihi durasi yang ditentukan</small>
                    </td>
                    <td class="text-right">1</td>
                    <td class="text-right">${formatCurrency(overtimeAmount)}</td>
                  </tr>
                  ` : ''}
                  <tr style="background: #f8fafc;">
                    <td colspan="3" style="padding: 8px 12px; font-weight: 600; color: #1e293b; border-top: 2px solid #e2e8f0;">
                      Pembagian Pembayaran:
                    </td>
                  </tr>
                  <tr>
                    <td style="padding-left: 24px;">
                      Deposit (50%)<br>
                      <small>Dibayar saat pemesanan</small>
                    </td>
                    <td class="text-right">1</td>
                    <td class="text-right">${formatCurrency(depositAmount)}</td>
                  </tr>
                  <tr>
                    <td style="padding-left: 24px;">
                      Sisa Pembayaran (50%)${overtimeAmount > 0 ? ' + Overtime' : ''}<br>
                      <small>Dibayar setelah operasi selesai</small>
                    </td>
                    <td class="text-right">1</td>
                    <td class="text-right">${formatCurrency(remainingAmount + overtimeAmount)}</td>
                  </tr>
                  <tr class="total-row">
                    <td colspan="2" class="text-right"><strong>
                      ${invoice.payment?.status === "FULLY_PAID" ? "Total Dibayar" :
                        invoice.payment?.status === "DEPOSIT_PAID" ? "Sisa yang Harus Dibayar" :
                        "Total yang Harus Dibayar"}
                    </strong></td>
                    <td class="text-right"><strong>
                      ${invoice.payment?.status === "FULLY_PAID" ? formatCurrency(totalWithOvertime) :
                        invoice.payment?.status === "DEPOSIT_PAID" ? formatCurrency(remainingAmount + overtimeAmount) :
                        formatCurrency(depositAmount)}
                    </strong></td>
                  </tr>
                </tbody>
              </table>



              <div class="payment-info">
                <div class="section-title">Instruksi Pembayaran</div>
                <p>Mohon lakukan pembayaran ke rekening:</p>
                <table>
                  <tr>
                    <td style="width: 30%"><strong>Bank</strong></td>
                    <td>Bank BCA</td>
                  </tr>
                  <tr>
                    <td><strong>No. Rekening</strong></td>
                    <td>**********</td>
                  </tr>
                  <tr>
                    <td><strong>Atas Nama</strong></td>
                    <td>PT Rental Genset</td>
                  </tr>
                </table>
              </div>

              <div class="footer">
                <p>Invoice ini dibuat secara otomatis dan sah tanpa tanda tangan.</p>
                <p>Jika ada pertanyaan tentang invoice ini, silakan hubungi <NAME_EMAIL></p>
                <p>Rental Genset &copy; ${new Date().getFullYear()}</p>
              </div>
            </div>
          </div>
        </body>
      </html>
    `;

    // Kirim HTML langsung untuk dilihat dan dicetak
    return new NextResponse(html, {
      headers: {
        "Content-Type": "text/html",
      }
    });

  } catch (error) {
    console.error("Error generating invoice:", error);
    return NextResponse.json(
      { error: "Failed to generate invoice" },
      { status: 500 }
    );
  }
}