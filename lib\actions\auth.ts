"use server";

import { prisma } from "@/lib/config/prisma";
import { hash } from "bcrypt-ts";
import { AuthResult } from "@/lib/types/auth";
import { Role } from "@/lib/types/auth";
import { signIn } from "@/auth";

export async function signInCredentials(
    prevState: unknown,
    formData: FormData
): Promise<AuthResult> {
    try {
        const email = formData.get("email") as string;
        const password = formData.get("password") as string;

        if (!email || !password) {
            return {
                success: false,
                message: "Email dan password harus diisi"
            };
        }

        const signInResult = await signIn("credentials", {
            email: email.toLowerCase().trim(),
            password,
            redirect: false,
        });

        // Log untuk debugging
        console.log("Sign in attempt:", {
            email,
            timestamp: new Date().toISOString(),
            result: signInResult
        });

        if (signInResult?.error) {
            return {
                success: false,
                message: "<PERSON>ail atau password salah"
            };
        }

        return {
            success: true,
            message: "Login berhasil",
            redirectUrl: signInResult?.url || "/dashboard"
        };
    } catch (error) {
        console.error("Login error:", {
            error,
            timestamp: new Date().toISOString()
        });
        
        return {
            success: false,
            message: "Terjadi kesalahan sistem"
        };
    }
}

export async function signupCredentials(data: {
    name: string;
    email: string;
    phone: string;
    password: string;
    confirmPassword: string;
}) {
    try {
        // Normalize email
        const normalizedEmail = data.email.toLowerCase();

        // Cek email sudah terdaftar
        const existingUser = await prisma.user.findUnique({
            where: { email: normalizedEmail }
        });

        if (existingUser) {
            return {
                success: true,
                errors: { email: ["Email sudah terdaftar"] }
            };
        }

        // Hash password
        const hashedPassword = await hash(data.password, 10);

        // Buat user baru
        const newUser = await prisma.user.create({
            data: {
                name: data.name,
                email: normalizedEmail,
                phone: data.phone,
                password: hashedPassword,
                role: "USER"
            }
        });

        console.log("User created:", {
            id: newUser.id,
            email: newUser.email,
            role: newUser.role
        });

        return { success: true };
    } catch (error) {
        console.error("Register error:", error);
        return {
            success: false,
            errors: { _form: ["Terjadi kesalahan sistem"] }
        };
    }
}

export async function createTestUser() {
    try {
        // Hapus user test yang mungkin sudah ada
        await prisma.user.deleteMany({
            where: {
                email: "<EMAIL>"
            }
        });

    

        return { success: true };
    } catch (error) {
        console.error("Error creating test user:", error);
        return { success: true };
    }
}

export async function updateUserRole(userId: string, newRole: Role): Promise<{ success: boolean; message: string }> {
    try {
        const response = await fetch('/api/users/role', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ userId, role: newRole })
        });

        if (!response.ok) {
            throw new Error('Failed to update role');
        }

        await response.json(); // Tunggu response tapi tidak perlu simpan datanya
        
        return {
            success: true,
            message: `Role berhasil diubah menjadi ${newRole}`
        };
    } catch (error) {
        console.error("Update role error:", error);
        return {
            success: false,
            message: "Gagal mengubah role"
        };
    }
}
