export const RENTAL_DURATIONS = {
  '1x8_HOURS': { 
    label: '8 Jam', 
    hours: 8,
    description: 'Sewa 8 jam'
  },
  '2x8_HOURS': { 
    label: '16 Jam', 
    hours: 16,
    description: 'Sewa 16 jam'
  },
  '3x8_HOURS': { 
    label: '24 Jam', 
    hours: 24,
    description: 'Sewa 24 jam'
  },
  '4x8_HOURS': { 
    label: '32 Jam', 
    hours: 32,
    description: 'Sewa 32 jam'
  },
  '5x8_HOURS': { 
    label: '40 Jam', 
    hours: 40,
    description: 'Sewa 40 jam'
  },
  '6x8_HOURS': { 
    label: '48 Jam', 
    hours: 48,
    description: 'Sewa 48 jam'
  },
  '7x8_HOURS': { 
    label: '56 Jam', 
    hours: 56,
    description: 'Sewa 56 jam'
  },
  '8x8_HOURS': { 
    label: '64 Jam', 
    hours: 64,
    description: 'Sewa 64 jam'
  },
  '9x8_HOURS': { 
    label: '72 Jam', 
    hours: 72,
    description: 'Sewa 72 jam'
  },
  '10x8_HOURS': { 
    label: '80 Jam', 
    hours: 80,
    description: 'Sewa 80 jam'
  }
} as const;

export type RentalDuration = keyof typeof RENTAL_DURATIONS;

// Konstanta untuk validasi
export const MAX_RENTAL_DAYS = 30;
export const MIN_RENTAL_DAYS = 1;
export const HOURS_PER_DAY = 24;
export const SHIFT_HOURS = 8;

// Konstanta untuk perhitungan harga
export const BASE_OVERTIME_RATE = 100000; // Rp 100.000/jam untuk 10 KVA
export const MIN_OVERTIME_RATE = 50000; // Minimal Rp 50.000/jam
export const OVERTIME_MULTIPLIER = 1.5; // 1.5x dari harga normal 
