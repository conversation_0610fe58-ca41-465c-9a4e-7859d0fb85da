"use server";

import { prisma } from "@/lib/config/prisma";
import { auth } from "@/auth";

export async function getUserStatus() {
    try {
        const session = await auth();
        if (!session?.user) {
            return { isKnownUser: false };
        }

        const userId = session.user.id;
        
        // Cek jumlah rental yang sudah selesai
        const completedRentals = await prisma.rental.count({
            where: {
                userId: userId,
                status: "COMPLETED",
            },
        });
        
        // User dianggap dikenal jika memiliki lebih dari 2 rental yang selesai
        const isKnownUser = completedRentals >= 2;
        
        return { isKnownUser, completedRentals };
    } catch (error) {
        console.error("Error getting user status:", error);
        return { isKnownUser: false, error: "Failed to get user status" };
    }
} 
