import { auth } from "@/auth";
import { redirect, notFound } from "next/navigation";
import { getProductById } from "@/lib/data/product";
import { RentalFormNew } from "@/app/components/rental/rental-form-new";
import { BackButton } from "@/app/components/ui/back-button";
import { LuPackage } from "react-icons/lu";
import Image from "next/image";

export const metadata = {
    title: "Form Penyewaan Genset",
    description: "Form penyewaan genset - Rental Ganset"
};

interface Props {
    params: { id: string };
}

export default async function RentPage({ params }: Props) {
    const { id } = await Promise.resolve(params);
    const session = await auth();

    if (!session?.user) {
        redirect('/login');
    }

    try {
        console.log("Mencari produk dengan ID:", id);
        const product = await getProductById(id);

        if (!product) {
            console.log("Produk tidak ditemukan untuk ID:", id);
            notFound();
        }

        console.log("Produk ditemukan:", product.name);

        return (
            <div className="min-h-screen bg-gray-50 dark:bg-gray-900 pb-12">
                {/* Header section with back button */}
                <div className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 mb-6">
                    <div className="max-w-5xl mx-auto px-4 py-4 flex items-center justify-between">
                        <div className="flex items-center gap-2">
                            <BackButton
                                href={`/user/catalog/${id}`}
                                label="Kembali ke detail produk"
                            />
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                            {session.user.email}
                        </div>
                    </div>
                </div>

                <div className="max-w-5xl mx-auto px-4">
                    <div className="grid md:grid-cols-3 gap-8">
                        {/* Product info sidebar */}
                        <div className="md:col-span-1">
                            <div className="bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-lg sticky top-6">
                                <div className="aspect-w-1 aspect-h-1 w-full relative">
                                    {product.imageUrl ? (
                                        <div className="relative h-48 w-full overflow-hidden">
                                            <Image
                                                src={product.imageUrl}
                                                alt={product.name}
                                                fill
                                                sizes="(max-width: 768px) 100vw, 33vw"
                                                priority
                                                className="object-cover"
                                            />
                                        </div>
                                    ) : (
                                        <div className="h-48 w-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                                            <LuPackage className="h-12 w-12 text-gray-400 dark:text-gray-500" />
                                        </div>
                                    )}
                                </div>
                                <div className="p-4">
                                    <div className="flex items-center justify-between mb-2">
                                        <h3 className="font-semibold text-lg text-gray-900 dark:text-white">{product.name}</h3>
                                        <span className="text-sm px-2 py-1 bg-violet-100 dark:bg-violet-900 text-violet-800 dark:text-violet-200 rounded-full font-medium">
                                            {product.status || 'Tersedia'}
                                        </span>
                                    </div>
                                    <p className="text-gray-600 dark:text-gray-300 text-sm mb-3">{product.description}</p>
                                    <div className="border-t border-gray-100 dark:border-gray-700 pt-3 mt-3">
                                        <div className="flex justify-between items-center mb-2">
                                            <span className="text-gray-600 dark:text-gray-400 text-sm">Harga Sewa</span>
                                            <span className="font-bold text-lg text-violet-600 dark:text-violet-400">Rp {product.price?.toLocaleString('id-ID')}/hari</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-gray-600 dark:text-gray-400 text-sm ">Stok</span>
                                            <span className="font-medium dark:text-violet-400">{product.stock || 0} unit</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Form section */}
                        <div className="md:col-span-2">
                            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
                                <div className="p-6 border-b border-gray-100 dark:border-gray-700">
                                    <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Form Penyewaan Genset</h1>
                                    <p className="text-gray-600 dark:text-gray-400 mt-1">Lengkapi informasi berikut untuk menyewa {product.name}</p>
                                </div>
                                <div className="p-6">
                                    <RentalFormNew product={product} />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        );
    } catch (error) {
        console.error("Error:", error);
        throw new Error("Gagal memuat data produk");
    }
}