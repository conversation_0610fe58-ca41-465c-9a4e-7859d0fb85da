"use client";

import { usePathname } from "next/navigation";
import Link from "next/link";
import { cn } from "@/lib/utils";
import { Button } from "@/app/components/ui/button";
import {
  LuMenu,
  LuLayoutDashboard as LuDashboard,
  LuShoppingCart,
  LuUsers,
  LuCreditCard,
  LuPackage,
  LuSettings,
  LuUser
} from "react-icons/lu";
import { ScrollArea } from "@/app/components/ui/scroll-area";
import { <PERSON><PERSON>, <PERSON>et<PERSON>ontent, SheetTrigger, SheetTitle } from "@/app/components/ui/sheet";
import { useEffect, useState } from "react";
import { NotificationBadge } from "@/app/components/shared/notification/notification-badge";
import { ThemeToggle } from "@/app/components/ui/theme-toggle";

interface AdminLayoutProps {
  children: React.ReactNode;
}

interface UserData {
  id?: string;
  name?: string;
  email?: string;
  role?: string;
}

const navigation = [
  {
    name: "Dashboard",
    href: "/admin/dashboard",
    icon: LuDashboard,
    description: "Ikhtisar sistem dan analitik",
    badge: null
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    href: "/admin/users",
    icon: LuUsers,
    description: "Kelola akun dan hak akses",
    badge: null
  },
  {
    name: "Produk",
    href: "/admin/products",
    icon: LuPackage,
    description: "Manajemen inventori genset",
    badge: null
  },
  {
    name: "Penyewaan",
    href: "/admin/rentals",
    icon: LuShoppingCart,
    description: "Monitor transaksi rental",
    badge: "active"
  },
  {
    name: "Pembayaran",
    href: "/admin/payments",
    icon: LuCreditCard,
    description: "Kelola invoice dan pembayaran",
    badge: null
  },
  {
    name: "Operasional",
    href: "/admin/operations",
    icon: LuSettings,
    description: "Status operasi genset",
    badge: null
  },
  {
    name: "Profil",
    href: "/admin/profile",
    icon: LuUser,
    description: "Pengaturan akun admin",
    badge: null
  },
];

export default function AdminLayout({ children }: AdminLayoutProps) {
  const pathname = usePathname();
  const [user, setUser] = useState<UserData | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Fetch user data from server
    const fetchUserData = async () => {
      try {
        setIsLoading(true);
        // Use relative URL to automatically handle base path
        const response = await fetch('/api/users/me', {
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json'
          }
        });
        if (response.ok) {
          const userData = await response.json();
          setUser(userData);
        }
      } catch (error) {
        console.error("Error fetching user data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserData();
  }, []);

  return (
    <div className="flex min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20 dark:from-slate-950 dark:via-blue-950/30 dark:to-indigo-950/20">
      {/* Sidebar untuk desktop */}
      <aside className="hidden lg:flex lg:w-72 lg:flex-col lg:fixed lg:inset-y-0 z-50">
        <div className="flex flex-col flex-1 min-h-0 bg-white/95 backdrop-blur-xl border-r border-slate-200/60 shadow-xl dark:bg-slate-900/95 dark:border-slate-700/60">
          {/* Header dengan logo modern */}
          <div className="flex items-center h-20 px-6 border-b border-slate-200/60 bg-gradient-to-r from-blue-500 via-blue-600 to-indigo-600 dark:from-blue-800 dark:via-blue-900 dark:to-indigo-900 dark:border-slate-700/60 shadow-lg">
            <Link href="/admin/dashboard" className="flex items-center gap-3 group">
              <div className="w-12 h-12 bg-white/25 backdrop-blur-sm rounded-2xl flex items-center justify-center group-hover:bg-white/35 transition-all duration-300 group-hover:scale-105 shadow-lg dark:bg-gray-100/20 dark:group-hover:bg-gray-100/30">
                <span className="text-2xl font-bold text-white drop-shadow-md dark:text-gray-50 dark:drop-shadow-lg">RG</span>
              </div>
              <div className="flex flex-col">
                <span className="text-xl font-bold text-white drop-shadow-md dark:text-gray-50 dark:drop-shadow-lg">RentalGenset</span>
                <span className="text-xs text-white/95 font-semibold dark:text-gray-100 dark:font-bold">Admin Panel</span>
              </div>
            </Link>
          </div>
          <ScrollArea className="flex-1 py-6">
            {/* Navigation Section */}
            <div className="px-4">
              <div className="mb-6">
                <h3 className="text-xs font-semibold text-slate-500 dark:text-slate-400 uppercase tracking-wider mb-3">
                  Menu Utama
                </h3>
                <nav className="space-y-1">
                  {navigation.map((item) => {
                    const isActive = pathname === item.href || pathname.startsWith(`${item.href}/`);
                    return (
                      <Link
                        key={item.name}
                        href={item.href}
                        className={cn(
                          "group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 relative",
                          isActive
                            ? "bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-700 shadow-sm border border-blue-100 dark:from-blue-900/40 dark:to-indigo-900/40 dark:text-blue-300 dark:border-blue-800/50"
                            : "text-slate-700 hover:bg-slate-50 hover:text-slate-900 dark:text-slate-300 dark:hover:bg-slate-800/50 dark:hover:text-slate-100"
                        )}
                      >
                        <div className={cn(
                          "mr-3 p-2 rounded-lg transition-all duration-200",
                          isActive
                            ? "bg-blue-100 text-blue-600 dark:bg-blue-800/50 dark:text-blue-300"
                            : "bg-slate-100 text-slate-500 group-hover:bg-slate-200 dark:bg-slate-800 dark:text-slate-400 dark:group-hover:bg-slate-700"
                        )}>
                          <item.icon className="h-5 w-5" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between">
                            <span className="font-medium">{item.name}</span>
                            {item.badge && (
                              <span className="ml-2 px-2 py-0.5 text-xs font-medium bg-emerald-100 text-emerald-700 rounded-full dark:bg-emerald-900/30 dark:text-emerald-400">
                                {item.badge}
                              </span>
                            )}
                          </div>
                          <p className="text-xs text-slate-500 dark:text-slate-400 mt-0.5 truncate">
                            {item.description}
                          </p>
                        </div>
                        {isActive && (
                          <div className="absolute right-0 top-1/2 -translate-y-1/2 w-1 h-8 bg-blue-600 rounded-l-full dark:bg-blue-400" />
                        )}
                      </Link>
                    );
                  })}
                </nav>
              </div>
            </div>
          </ScrollArea>
          {/* User Profile Section */}
          {user && (
            <div className="border-t border-slate-200/60 dark:border-slate-700/60 p-4">
              <div className="bg-gradient-to-r from-slate-50 to-blue-50/50 dark:from-slate-800/50 dark:to-blue-900/20 rounded-xl p-4">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center shadow-lg">
                    <span className="text-white font-bold text-lg">
                      {user.name?.[0] || "A"}
                    </span>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-semibold text-slate-900 dark:text-slate-100 truncate">
                      {user.name || "Admin"}
                    </p>
                    <p className="text-xs text-slate-500 dark:text-slate-400 truncate">
                      {user.email || "<EMAIL>"}
                    </p>
                    <div className="flex items-center gap-1 mt-1">
                      <div className="w-2 h-2 bg-emerald-400 rounded-full"></div>
                      <span className="text-xs text-emerald-600 dark:text-emerald-400 font-medium">
                        Online
                      </span>
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-between pt-3 border-t border-slate-200/60 dark:border-slate-700/60">
                  <div className="flex items-center gap-2">
                    <ThemeToggle />
                    <NotificationBadge />
                  </div>
                  <Link href="/admin/profile">
                    <Button variant="ghost" size="sm" className="text-xs h-8 px-3 text-slate-600 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700">
                      Profil
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          )}
        </div>
      </aside>

      {/* Mobile Sidebar */}
      <Sheet>
        <SheetTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className="lg:hidden fixed top-4 left-4 z-50 h-11 w-11 bg-white/90 backdrop-blur-sm border border-blue-200 shadow-lg hover:bg-white text-blue-600 hover:text-blue-700 dark:bg-slate-800/90 dark:border-blue-700 dark:text-blue-400 dark:hover:bg-slate-800 dark:hover:text-blue-300 rounded-xl"
          >
            <LuMenu className="h-5 w-5" />
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="p-0 w-80 bg-white/95 backdrop-blur-xl dark:bg-slate-900/95">
          <SheetTitle className="sr-only">Menu Navigasi Admin</SheetTitle>
          <div className="flex flex-col min-h-screen">
            {/* Mobile Header */}
            <div className="flex items-center h-20 px-6 border-b border-slate-200/60 bg-gradient-to-r from-blue-500 via-blue-600 to-indigo-600 dark:from-blue-800 dark:via-blue-900 dark:to-indigo-900 dark:border-slate-700/60 shadow-lg">
              <Link href="/admin/dashboard" className="flex items-center gap-3">
                <div className="w-12 h-12 bg-white/25 backdrop-blur-sm rounded-2xl flex items-center justify-center shadow-lg dark:bg-gray-100/20">
                  <span className="text-2xl font-bold text-white drop-shadow-md dark:text-gray-50 dark:drop-shadow-lg">RG</span>
                </div>
                <div className="flex flex-col">
                  <span className="text-xl font-bold text-white drop-shadow-md dark:text-gray-50 dark:drop-shadow-lg">RentalGenset</span>
                  <span className="text-xs text-white/95 font-semibold dark:text-gray-100 dark:font-bold">Admin Panel</span>
                </div>
              </Link>
            </div>
            <ScrollArea className="flex-1 py-6">
              <div className="px-4">
                <div className="mb-6">
                  <h3 className="text-xs font-semibold text-slate-500 dark:text-slate-400 uppercase tracking-wider mb-3">
                    Menu Utama
                  </h3>
                  <nav className="space-y-1">
                    {navigation.map((item) => {
                      const isActive = pathname === item.href || pathname.startsWith(`${item.href}/`);
                      return (
                        <Link
                          key={item.name}
                          href={item.href}
                          className={cn(
                            "group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 relative",
                            isActive
                              ? "bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-700 shadow-sm border border-blue-100 dark:from-blue-900/40 dark:to-indigo-900/40 dark:text-blue-300 dark:border-blue-800/50"
                              : "text-slate-700 hover:bg-slate-50 hover:text-slate-900 dark:text-slate-300 dark:hover:bg-slate-800/50 dark:hover:text-slate-100"
                          )}
                        >
                          <div className={cn(
                            "mr-3 p-2 rounded-lg transition-all duration-200",
                            isActive
                              ? "bg-blue-100 text-blue-600 dark:bg-blue-800/50 dark:text-blue-300"
                              : "bg-slate-100 text-slate-500 group-hover:bg-slate-200 dark:bg-slate-800 dark:text-slate-400 dark:group-hover:bg-slate-700"
                          )}>
                            <item.icon className="h-5 w-5" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between">
                              <span className="font-medium">{item.name}</span>
                              {item.badge && (
                                <span className="ml-2 px-2 py-0.5 text-xs font-medium bg-emerald-100 text-emerald-700 rounded-full dark:bg-emerald-900/30 dark:text-emerald-400">
                                  {item.badge}
                                </span>
                              )}
                            </div>
                            <p className="text-xs text-slate-500 dark:text-slate-400 mt-0.5 truncate">
                              {item.description}
                            </p>
                          </div>
                          {isActive && (
                            <div className="absolute right-0 top-1/2 -translate-y-1/2 w-1 h-8 bg-blue-600 rounded-l-full dark:bg-blue-400" />
                          )}
                        </Link>
                      );
                    })}
                  </nav>
                </div>
              </div>
            </ScrollArea>
            {/* Mobile User Profile */}
            {user && (
              <div className="border-t border-slate-200/60 dark:border-slate-700/60 p-4">
                <div className="bg-gradient-to-r from-slate-50 to-blue-50/50 dark:from-slate-800/50 dark:to-blue-900/20 rounded-xl p-4">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center shadow-lg">
                      <span className="text-white font-bold text-lg">
                        {user.name?.[0] || "A"}
                      </span>
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-semibold text-slate-900 dark:text-slate-100 truncate">
                        {user.name || "Admin"}
                      </p>
                      <p className="text-xs text-slate-500 dark:text-slate-400 truncate">
                        {user.email || "<EMAIL>"}
                      </p>
                      <div className="flex items-center gap-1 mt-1">
                        <div className="w-2 h-2 bg-emerald-400 rounded-full"></div>
                        <span className="text-xs text-emerald-600 dark:text-emerald-400 font-medium">
                          Online
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between pt-3 border-t border-slate-200/60 dark:border-slate-700/60">
                    <div className="flex items-center gap-2">
                      <ThemeToggle />
                      <NotificationBadge />
                    </div>
                    <Link href="/admin/profile">
                      <Button variant="ghost" size="sm" className="text-xs h-8 px-3 text-slate-600 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700">
                        Profil
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>
            )}
          </div>
        </SheetContent>
      </Sheet>

      {/* Main Content */}
      <main className="flex-1 lg:pl-72">
        {/* Modern Header */}
        <header className="sticky top-0 z-40 bg-white/98 backdrop-blur-xl border-b border-slate-200/60 dark:bg-slate-900/98 dark:border-slate-700/60 shadow-lg">
          <div className="flex items-center justify-between h-16 px-4 sm:px-6 lg:px-8">
            <div className="flex items-center gap-4">
              <div className="flex flex-col lg:hidden">
                <h1 className="text-lg font-bold text-slate-900 dark:text-slate-100">
                  RentalGenset
                </h1>
                <p className="text-xs text-blue-600 dark:text-blue-400 font-semibold">
                  Admin Panel
                </p>
              </div>
            </div>

            {/* Header Actions */}
            <div className="flex items-center gap-3">
              <div className="hidden lg:flex items-center gap-2">
                <ThemeToggle />
                <NotificationBadge />
              </div>

              {/* Mobile user info */}
              {user && (
                <div className="lg:hidden flex items-center gap-2">
                  <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center">
                    <span className="text-white font-medium text-sm">
                      {user.name?.[0] || "A"}
                    </span>
                  </div>
                  <div className="flex items-center gap-1">
                    <ThemeToggle />
                    <NotificationBadge />
                  </div>
                </div>
              )}
            </div>
          </div>
        </header>

        {/* Content Area */}
        <div className="flex-1 overflow-y-auto">
          <div className="px-4 sm:px-6 lg:px-8 py-6 max-w-7xl mx-auto">
            {/* Loading State */}
            {isLoading ? (
              <div className="flex items-center justify-center py-12">
                <div className="flex items-center gap-3">
                  <div className="w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                  <span className="text-slate-600 dark:text-slate-400">Memuat...</span>
                </div>
              </div>
            ) : (
              children
            )}
          </div>
        </div>
      </main>
    </div>
  );
}
