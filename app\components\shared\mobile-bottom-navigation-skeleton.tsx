"use client";

import { Skeleton } from "@/app/components/ui/skeleton";
import { cn } from "@/lib/utils";

export function MobileBottomNavigationSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn(
      "lg:hidden fixed bottom-0 left-0 right-0 bg-white/95 backdrop-blur-xl border-t border-violet-200/60 dark:bg-slate-900/95 dark:border-violet-700/60 shadow-lg z-40",
      className
    )}>
      <div className="grid grid-cols-3 h-16">
        {/* Navigation Items with Realistic Widths */}
        {[
          { width: "w-12", name: "<PERSON><PERSON><PERSON>" },   // 7 karakter
          { width: "w-14", name: "Katalog" },   // 7 karakter  
          { width: "w-12", name: "<PERSON><PERSON>" }     // 6 karakter (shortened from "Rental Saya")
        ].map((item, index) => (
          <div 
            key={index} 
            className="flex flex-col items-center justify-center px-1 py-2"
            style={{
              animationDelay: `${index * 100}ms`
            }}
          >
            {/* Icon Skeleton */}
            <Skeleton 
              variant="navigation"
              shape="circle"
              className="w-5 h-5 mb-1"
            />
            
            {/* Text Skeleton with Realistic Width */}
            <Skeleton 
              variant="mobile"
              shape="rounded"
              className={cn("h-3", item.width)}
            />
          </div>
        ))}
      </div>
    </div>
  );
}

// Enhanced version with shimmer effect
export function MobileBottomNavigationSkeletonShimmer({ className }: { className?: string }) {
  return (
    <div className={cn(
      "lg:hidden fixed bottom-0 left-0 right-0 bg-white/95 backdrop-blur-xl border-t border-violet-200/60 dark:bg-slate-900/95 dark:border-violet-700/60 shadow-lg z-40",
      className
    )}>
      <div className="grid grid-cols-3 h-16">
        {[
          { width: "w-12", name: "Beranda" },
          { width: "w-14", name: "Katalog" },
          { width: "w-12", name: "Rental" }
        ].map((item, index) => (
          <div 
            key={index} 
            className="flex flex-col items-center justify-center px-1 py-2"
          >
            {/* Icon Skeleton with shimmer */}
            <div 
              className="w-5 h-5 mb-1 skeleton-shimmer rounded"
              style={{ animationDelay: `${index * 150}ms` }}
            />
            
            {/* Text Skeleton with shimmer */}
            <div 
              className={cn("h-3 skeleton-shimmer rounded", item.width)}
              style={{ animationDelay: `${index * 150 + 75}ms` }}
            />
          </div>
        ))}
      </div>
    </div>
  );
}

// Realistic version that matches actual component structure
export function RealisticMobileBottomNavigationSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn(
      "lg:hidden fixed bottom-0 left-0 right-0 bg-white/95 backdrop-blur-xl border-t border-violet-200/60 dark:bg-slate-900/95 dark:border-violet-700/60 shadow-lg z-40",
      className
    )}>
      <div className="grid grid-cols-3 h-16">
        {/* Beranda */}
        <div className="flex flex-col items-center justify-center px-1 py-2">
          <Skeleton 
            variant="navigation"
            shape="circle"
            className="w-5 h-5 mb-1 bg-violet-100/80 dark:bg-violet-900/30"
          />
          <Skeleton 
            variant="mobile"
            shape="rounded"
            className="h-3 w-12 bg-violet-50/90 dark:bg-violet-950/40"
          />
        </div>

        {/* Katalog */}
        <div className="flex flex-col items-center justify-center px-1 py-2">
          <Skeleton 
            variant="navigation"
            shape="circle"
            className="w-5 h-5 mb-1 bg-violet-100/80 dark:bg-violet-900/30"
          />
          <Skeleton 
            variant="mobile"
            shape="rounded"
            className="h-3 w-14 bg-violet-50/90 dark:bg-violet-950/40"
          />
        </div>

        {/* Rental (shortened from "Rental Saya" for mobile) */}
        <div className="flex flex-col items-center justify-center px-1 py-2">
          <Skeleton 
            variant="navigation"
            shape="circle"
            className="w-5 h-5 mb-1 bg-violet-100/80 dark:bg-violet-900/30"
          />
          <Skeleton 
            variant="mobile"
            shape="rounded"
            className="h-3 w-12 bg-violet-50/90 dark:bg-violet-950/40"
          />
        </div>
      </div>
    </div>
  );
}

// Loading state for when bottom navigation is being initialized
export function MobileBottomNavigationLoadingState({ className }: { className?: string }) {
  return (
    <div className={cn(
      "lg:hidden fixed bottom-0 left-0 right-0 bg-white/95 backdrop-blur-xl border-t border-violet-200/60 dark:bg-slate-900/95 dark:border-violet-700/60 shadow-lg z-40",
      className
    )}>
      <div className="grid grid-cols-3 h-16">
        {Array.from({ length: 3 }).map((_, index) => (
          <div 
            key={index} 
            className="flex flex-col items-center justify-center px-1 py-2 opacity-60"
            style={{
              animationDelay: `${index * 200}ms`,
              animation: 'fadeIn 0.5s ease-out forwards'
            }}
          >
            <div className="w-5 h-5 mb-1 bg-gradient-to-r from-violet-100/60 to-purple-100/60 dark:from-violet-900/20 dark:to-purple-900/20 rounded animate-pulse" />
            <div className="h-3 w-10 bg-gradient-to-r from-violet-50/80 to-purple-50/80 dark:from-violet-950/30 dark:to-purple-950/30 rounded animate-pulse" />
          </div>
        ))}
      </div>
      
      {/* CSS Animation */}
      <style jsx>{`
        @keyframes fadeIn {
          from {
            opacity: 0;
            transform: translateY(10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
      `}</style>
    </div>
  );
}
