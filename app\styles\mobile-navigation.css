/* Mobile Navigation Optimization CSS */

/* Base mobile optimizations */
@media (max-width: 768px) {
  
  /* Touch target optimizations */
  .mobile-nav-item,
  .mobile-nav-button,
  .mobile-touch-target {
    min-height: 44px !important;
    min-width: 44px !important;
    touch-action: manipulation !important;
    -webkit-tap-highlight-color: transparent !important;
  }

  /* Improved touch feedback */
  .mobile-nav-item:active,
  .mobile-nav-button:active {
    transform: scale(0.98) !important;
    transition: transform 0.1s ease-out !important;
  }

  /* Mobile menu panel optimizations */
  .mobile-menu-panel {
    width: min(320px, 85vw) !important;
    height: 100vh !important;
    overflow-y: auto !important;
    -webkit-overflow-scrolling: touch !important;
    overscroll-behavior: contain !important;
  }

  /* Prevent body scroll when mobile menu is open */
  .mobile-menu-open {
    overflow: hidden !important;
    position: fixed !important;
    width: 100% !important;
  }

  /* Mobile navigation spacing */
  .mobile-nav-spacing {
    padding: 12px 16px !important;
    gap: 12px !important;
  }

  /* Mobile typography optimizations */
  .mobile-nav-text {
    font-size: 16px !important; /* Prevents zoom on iOS */
    line-height: 1.4 !important;
  }

  /* Mobile avatar optimizations */
  .mobile-avatar {
    width: 40px !important;
    height: 40px !important;
    font-size: 16px !important;
  }

  /* Mobile dropdown optimizations */
  .mobile-dropdown {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    z-index: 9999 !important;
    background: rgba(0, 0, 0, 0.5) !important;
    backdrop-filter: blur(4px) !important;
  }

  .mobile-dropdown-content {
    position: absolute !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    max-height: 80vh !important;
    overflow-y: auto !important;
    border-radius: 16px 16px 0 0 !important;
  }
}

/* Tablet optimizations */
@media (min-width: 769px) and (max-width: 1023px) {
  
  .tablet-nav-item {
    min-height: 42px !important;
    min-width: 42px !important;
    padding: 10px 16px !important;
  }

  .tablet-nav-text {
    font-size: 15px !important;
  }

  .tablet-avatar {
    width: 36px !important;
    height: 36px !important;
  }
}

/* Desktop optimizations */
@media (min-width: 1024px) {
  
  .desktop-nav-item {
    min-height: 44px !important;
    padding: 10px 16px !important;
  }

  .desktop-nav-text {
    font-size: 14px !important;
  }

  .desktop-avatar {
    width: 40px !important;
    height: 40px !important;
  }

  /* Desktop hover effects */
  .desktop-nav-item:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.15) !important;
  }
}

/* Animation keyframes for mobile navigation */
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Mobile menu animations */
.mobile-menu-enter {
  animation: slideInRight 0.3s ease-out forwards;
}

.mobile-menu-exit {
  animation: slideInRight 0.2s ease-in reverse;
}

.mobile-backdrop-enter {
  animation: fadeInScale 0.2s ease-out forwards;
}

.mobile-backdrop-exit {
  animation: fadeInScale 0.15s ease-in reverse;
}

/* Navigation item stagger animations */
.nav-item-stagger-1 { animation-delay: 0ms; }
.nav-item-stagger-2 { animation-delay: 50ms; }
.nav-item-stagger-3 { animation-delay: 100ms; }
.nav-item-stagger-4 { animation-delay: 150ms; }
.nav-item-stagger-5 { animation-delay: 200ms; }

/* Focus and accessibility improvements */
.nav-focus-visible:focus-visible {
  outline: 2px solid #8b5cf6 !important;
  outline-offset: 2px !important;
  border-radius: 8px !important;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .nav-item-active {
    border: 2px solid currentColor !important;
  }
  
  .nav-item-inactive {
    border: 1px solid currentColor !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .mobile-nav-item,
  .mobile-nav-button,
  .nav-animation {
    animation: none !important;
    transition: none !important;
  }
}

/* Dark mode specific optimizations */
@media (prefers-color-scheme: dark) {
  .mobile-menu-panel {
    background: rgba(17, 24, 39, 0.95) !important;
    backdrop-filter: blur(20px) !important;
  }
  
  .mobile-dropdown {
    background: rgba(0, 0, 0, 0.7) !important;
  }
}

/* Safe area insets for mobile devices with notches */
@supports (padding: max(0px)) {
  .mobile-nav-safe-area {
    padding-top: max(12px, env(safe-area-inset-top)) !important;
    padding-bottom: max(12px, env(safe-area-inset-bottom)) !important;
    padding-left: max(16px, env(safe-area-inset-left)) !important;
    padding-right: max(16px, env(safe-area-inset-right)) !important;
  }
}

/* Landscape mode optimizations for mobile */
@media (max-width: 768px) and (orientation: landscape) {
  .mobile-menu-panel {
    width: min(400px, 90vw) !important;
  }
  
  .mobile-nav-item {
    min-height: 40px !important;
    padding: 8px 16px !important;
  }
  
  .mobile-avatar {
    width: 32px !important;
    height: 32px !important;
  }
}
