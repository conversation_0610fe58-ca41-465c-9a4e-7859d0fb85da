"use client";

import Image from "next/image";
import { <PERSON>, Card<PERSON><PERSON>er, Card<PERSON><PERSON>le, Card<PERSON>ontent, CardFooter } from "@/app/components/ui/card";
import { Button } from "@/app/components/ui/button";
import { LuInfo } from "react-icons/lu";

interface Product {
  id: string;
  name: string;
  price: number;
  capacity: number | string;
  category: string | null;
  status: string;
  imageUrl?: string;
  image?: string;
  description?: string;
}

interface ProductSummaryProps {
  product: Product;
  durationDays: number;
  totalPrice: number;
  isKnownUser: boolean;
  isSubmitting: boolean;
  isFormValid: boolean;
  onConfirmOrder: () => void;
}

export function ProductSummary({
  product,
  durationDays,
  totalPrice,
  isKnownUser,
  isSubmitting,
  isFormValid,
  onConfirmOrder
}: ProductSummaryProps) {
  // Format capacity for display
  const capacityValue = product?.capacity 
    ? typeof product.capacity === 'number'
      ? `${product.capacity} kVA`
      : product.capacity
    : '?';
  
  // Modifikasi bagian perhitungan total dan UI pembayaran
  const fullPrice = totalPrice;
  const downPayment = fullPrice * 0.5; // 50% dari total

  return (
    <Card>
      <CardHeader>
        <CardTitle>Rincian Produk</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="aspect-video w-full overflow-hidden bg-gray-100 relative rounded-md" style={{ minHeight: "250px" }}>
          {product.imageUrl || product.image ? (
            <Image
              src={product.imageUrl || product.image || "https://via.placeholder.com/400x300?text=Genset"}
              alt={product.name}
              fill
              priority
              sizes="(max-width: 768px) 100vw, 33vw"
              className="object-cover"
              onError={(e) => {
                (e.target as HTMLImageElement).src = "https://via.placeholder.com/400x300?text=Genset";
              }}
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-gray-200">
              <span className="text-gray-400">Tidak ada gambar</span>
            </div>
          )}
        </div>

        <div>
          <h3 className="font-bold text-lg">{product.name}</h3>
          <p className="text-muted-foreground text-sm">Kapasitas: {capacityValue}</p>
        </div>

        <div className="border-t pt-4">
          <div className="flex justify-between mb-2">
            <span>Harga Sewa</span>
            <span className="font-medium">
              {new Intl.NumberFormat('id-ID', {
                style: 'currency',
                currency: 'IDR',
                maximumFractionDigits: 0
              }).format(product.price)} / hari
            </span>
          </div>
          <div className="flex justify-between mb-2">
            <span>Durasi</span>
            <span className="font-medium">{durationDays} hari</span>
          </div>
          <div className="flex justify-between border-t pt-2 mt-2 font-bold">
            <span>Total</span>
            <span>
              {new Intl.NumberFormat('id-ID', {
                style: 'currency',
                currency: 'IDR',
                maximumFractionDigits: 0
              }).format(fullPrice)}
            </span>
          </div>
          
          {!isKnownUser && (
            <div className="flex justify-between mt-2 text-green-600 font-medium">
              <span>Pembayaran Awal (50%)</span>
              <span>
                {new Intl.NumberFormat('id-ID', {
                  style: 'currency',
                  currency: 'IDR',
                  maximumFractionDigits: 0
                }).format(downPayment)}
              </span>
            </div>
          )}
        </div>

        <div className="rounded-md bg-blue-50 p-4 text-sm flex items-start">
          <LuInfo className="h-5 w-5 text-blue-500 mr-2 mt-0.5 flex-shrink-0" />
          <div>
            <p className="font-medium text-blue-700">Informasi Pembayaran</p>
            {isKnownUser ? (
              <p className="text-blue-600 mt-1">
                Sebagai pelanggan tetap, Anda akan menerima invoice pembayaran penuh setelah mengonfirmasi pesanan.
              </p>
            ) : (
              <p className="text-blue-600 mt-1">
                Anda perlu membayar uang muka sebesar 50% dari total biaya untuk mengonfirmasi pesanan. Sisa pembayaran dapat dilunasi saat pengembalian genset.
              </p>
            )}
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Button 
          onClick={onConfirmOrder}
          disabled={isSubmitting || !isFormValid}
          className={`w-full ${isFormValid ? 'bg-green-600 hover:bg-green-700' : 'bg-gray-400'} text-white`}
          size="lg"
        >
          {isSubmitting ? "Memproses..." : "Konfirmasi Pesanan"}
        </Button>
      </CardFooter>
    </Card>
  );
} 
