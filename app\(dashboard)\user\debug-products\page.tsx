import { getProductByUser } from "@/lib/data/product";
import Link from "next/link";
import { Card, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card";

export default async function DebugProductsPage() {
  try {
    const products = await getProductByUser();

    return (
      <div className="container mx-auto py-6 px-4">
        <Card>
          <CardHeader>
            <CardTitle>Debug: Semua Produk di Database</CardTitle>
          </CardHeader>
          <CardContent>
            {products.length === 0 ? (
              <p className="text-gray-500">Tidak ada produk di database</p>
            ) : (
              <div className="space-y-4">
                {products.map((product) => (
                  <div key={product.id} className="border p-4 rounded-lg">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <h3 className="font-semibold text-lg">{product.name}</h3>
                        <p className="text-sm text-gray-600">{product.description}</p>
                        <p className="text-sm">
                          <strong>ID:</strong> <code className="bg-gray-100 px-2 py-1 rounded">{product.id}</code>
                        </p>
                        <p className="text-sm">
                          <strong>Status:</strong> {product.status}
                        </p>
                        <p className="text-sm">
                          <strong>Harga:</strong> Rp {product.price?.toLocaleString('id-ID')}/hari
                        </p>
                        <p className="text-sm">
                          <strong>Stok:</strong> {product.stock} unit
                        </p>
                      </div>
                      <div className="space-y-2">
                        <Link 
                          href={`/user/catalog/${product.id}`}
                          className="block w-full bg-blue-500 text-white text-center py-2 px-4 rounded hover:bg-blue-600"
                        >
                          Lihat Detail
                        </Link>
                        <Link 
                          href={`/user/catalog/${product.id}/rent`}
                          className="block w-full bg-green-500 text-white text-center py-2 px-4 rounded hover:bg-green-600"
                        >
                          Form Rental
                        </Link>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    );
  } catch (error) {
    console.error("Error fetching products:", error);
    return (
      <div className="container mx-auto py-6 px-4">
        <Card>
          <CardHeader>
            <CardTitle>Error</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-red-500">Gagal memuat data produk: {error instanceof Error ? error.message : 'Unknown error'}</p>
          </CardContent>
        </Card>
      </div>
    );
  }
}
