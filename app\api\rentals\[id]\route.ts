
import { auth } from "@/auth";
import { prisma } from "@/lib/config/prisma";
import { NextRequest, NextResponse } from "next/server";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    if (!id) {
      return NextResponse.json(
        { error: "ID rental tidak valid" },
        { status: 400 }
      );
    }

    // Ambil data rental berdasarkan ID
    const rental = await prisma.rental.findUnique({
      where: { id },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            capacity: true,
            price: true,
            overtimeRate: true,
            imageUrl: true,
          },
        },
        payment: true,
      },
    });

    if (!rental) {
      return NextResponse.json(
        { error: "Rental tidak ditemukan" },
        { status: 404 }
      );
    }

    // Verifikasi bahwa user yang mengakses adalah pemilik rental atau admin
    if (rental.userId !== session.user.id && session.user.role !== "ADMIN") {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Pastikan rental memiliki properti yang dibutuhkan invoice
    const invoiceData = {
      id: rental.id,
      startDate: rental.startDate,
      endDate: rental.endDate,
      amount: rental.amount,
      deliveryAddress: rental.address || undefined,
      product: {
        name: rental.product.name,
        capacity: rental.product.capacity,
      },
      payment: rental.payment
    };

    return NextResponse.json(invoiceData);
  } catch (error) {
    console.error("Error fetching rental:", error);
    return NextResponse.json(
      { error: "Gagal mengambil data rental" },
      { status: 500 }
    );
  }
}
