"use client";

import {
  RealisticNavigationSkeleton,
  BerandaSkeleton,
  KatalogSkeleton,
  RentalSayaSkeleton,
  ProfilSkeleton,
  MobileMenuSkeleton
} from "./nav-skeleton";
import { MobileBottomNavigationSkeleton } from "../shared/mobile-bottom-navigation-skeleton";
import { cn } from "@/lib/utils/cn";

// Demo component to show skeleton size differences
export function SkeletonSizeDemo() {
  return (
    <div className="p-8 space-y-12 bg-gray-50 dark:bg-gray-900 min-h-screen">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
          Skeleton Loading Demo
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mb-8">
          Demonstrasi ukuran skeleton yang realistis untuk setiap item navigasi
        </p>

        {/* Desktop Navigation Skeleton */}
        <section className="mb-12">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
            Desktop Navigation Skeleton
          </h2>
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <RealisticNavigationSkeleton variant="desktop" />

            {/* Individual Items for Comparison */}
            <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
              <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-4">
                Individual Items (Desktop):
              </h3>
              <div className="flex items-start space-x-4">
                <div className="text-center">
                  <div className="mb-2 p-2 bg-gray-100 dark:bg-gray-700 rounded">
                    <BerandaSkeleton variant="desktop" />
                  </div>
                  <p className="text-xs text-gray-500">Beranda</p>
                  <p className="text-xs text-violet-600 font-mono">w-20 (80px)</p>
                </div>
                <div className="text-center">
                  <div className="mb-2 p-2 bg-gray-100 dark:bg-gray-700 rounded">
                    <KatalogSkeleton variant="desktop" />
                  </div>
                  <p className="text-xs text-gray-500">Katalog</p>
                  <p className="text-xs text-violet-600 font-mono">w-16 (64px)</p>
                </div>
                <div className="text-center">
                  <div className="mb-2 p-2 bg-violet-100 dark:bg-violet-900/30 rounded border-2 border-violet-300 dark:border-violet-600">
                    <RentalSayaSkeleton variant="desktop" />
                  </div>
                  <p className="text-xs text-gray-500 font-bold">Rental Saya ⭐</p>
                  <p className="text-xs text-violet-600 font-mono font-bold">w-24 (96px)</p>
                  <p className="text-xs text-red-500 font-bold">TERPANJANG!</p>
                </div>
                <div className="text-center">
                  <div className="mb-2 p-2 bg-gray-100 dark:bg-gray-700 rounded">
                    <ProfilSkeleton variant="desktop" />
                  </div>
                  <p className="text-xs text-gray-500">Profil</p>
                  <p className="text-xs text-violet-600 font-mono">w-16 (64px)</p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Tablet Navigation Skeleton */}
        <section className="mb-12">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
            Tablet Navigation Skeleton
          </h2>
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <RealisticNavigationSkeleton variant="tablet" />

            <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
              <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-4">
                Individual Items (Tablet):
              </h3>
              <div className="flex items-center space-x-2">
                <div className="text-center">
                  <BerandaSkeleton variant="tablet" />
                  <p className="text-xs text-gray-500 mt-2">Beranda (w-14)</p>
                </div>
                <div className="text-center">
                  <KatalogSkeleton variant="tablet" />
                  <p className="text-xs text-gray-500 mt-2">Katalog (w-12)</p>
                </div>
                <div className="text-center">
                  <RentalSayaSkeleton variant="tablet" />
                  <p className="text-xs text-gray-500 mt-2 font-bold text-violet-600">
                    Rental Saya (w-18) ⭐
                  </p>
                </div>
                <div className="text-center">
                  <ProfilSkeleton variant="tablet" />
                  <p className="text-xs text-gray-500 mt-2">Profil (w-10)</p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Mobile Navigation Skeleton */}
        <section className="mb-12">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
            Mobile Navigation Skeleton
          </h2>
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Mobile Menu Button:
              </h3>
              <RealisticNavigationSkeleton variant="mobile" />
            </div>

            <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
              <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-4">
                Individual Items (Mobile):
              </h3>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <BerandaSkeleton variant="mobile" />
                  <p className="text-xs text-gray-500">Beranda (w-16)</p>
                </div>
                <div className="flex items-center justify-between">
                  <KatalogSkeleton variant="mobile" />
                  <p className="text-xs text-gray-500">Katalog (w-14)</p>
                </div>
                <div className="flex items-center justify-between">
                  <RentalSayaSkeleton variant="mobile" />
                  <p className="text-xs text-gray-500 font-bold text-violet-600">
                    Rental Saya (w-20) ⭐ TERPANJANG
                  </p>
                </div>
                <div className="flex items-center justify-between">
                  <ProfilSkeleton variant="mobile" />
                  <p className="text-xs text-gray-500">Profil (w-12)</p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Mobile Bottom Navigation Skeleton */}
        <section className="mb-12">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
            Mobile Bottom Navigation Skeleton
          </h2>
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="relative">
              <MobileBottomNavigationSkeleton />
              <div className="h-20"></div> {/* Spacer for fixed positioning */}
            </div>

            <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
              <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-4">
                Bottom Navigation Items:
              </h3>
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <p className="text-xs text-gray-500">Beranda (w-12)</p>
                </div>
                <div>
                  <p className="text-xs text-gray-500">Katalog (w-14)</p>
                </div>
                <div>
                  <p className="text-xs text-gray-500">Rental (w-12)</p>
                  <p className="text-xs text-gray-400">(shortened)</p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Size Comparison Chart */}
        <section className="mb-12">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
            Size Comparison Chart
          </h2>
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b border-gray-200 dark:border-gray-700">
                    <th className="text-left py-2 text-gray-600 dark:text-gray-400">Item</th>
                    <th className="text-center py-2 text-gray-600 dark:text-gray-400">Desktop</th>
                    <th className="text-center py-2 text-gray-600 dark:text-gray-400">Tablet</th>
                    <th className="text-center py-2 text-gray-600 dark:text-gray-400">Mobile</th>
                    <th className="text-center py-2 text-gray-600 dark:text-gray-400">Karakter</th>
                  </tr>
                </thead>
                <tbody className="text-gray-700 dark:text-gray-300">
                  <tr className="border-b border-gray-100 dark:border-gray-700">
                    <td className="py-2">Beranda</td>
                    <td className="text-center py-2">w-20 (80px)</td>
                    <td className="text-center py-2">w-14 (56px)</td>
                    <td className="text-center py-2">w-16 (64px)</td>
                    <td className="text-center py-2">7</td>
                  </tr>
                  <tr className="border-b border-gray-100 dark:border-gray-700">
                    <td className="py-2">Katalog</td>
                    <td className="text-center py-2">w-16 (64px)</td>
                    <td className="text-center py-2">w-12 (48px)</td>
                    <td className="text-center py-2">w-14 (56px)</td>
                    <td className="text-center py-2">7</td>
                  </tr>
                  <tr className="border-b border-gray-100 dark:border-gray-700 bg-violet-50 dark:bg-violet-900/20">
                    <td className="py-2 font-bold text-violet-600 dark:text-violet-400">Rental Saya ⭐</td>
                    <td className="text-center py-2 font-bold text-violet-600 dark:text-violet-400">w-24 (96px)</td>
                    <td className="text-center py-2 font-bold text-violet-600 dark:text-violet-400">w-18 (72px)</td>
                    <td className="text-center py-2 font-bold text-violet-600 dark:text-violet-400">w-20 (80px)</td>
                    <td className="text-center py-2 font-bold text-violet-600 dark:text-violet-400">11</td>
                  </tr>
                  <tr>
                    <td className="py-2">Profil</td>
                    <td className="text-center py-2">w-16 (64px)</td>
                    <td className="text-center py-2">w-10 (40px)</td>
                    <td className="text-center py-2">w-12 (48px)</td>
                    <td className="text-center py-2">6</td>
                  </tr>
                </tbody>
              </table>
            </div>

            <div className="mt-4 p-4 bg-violet-50 dark:bg-violet-900/20 rounded-lg">
              <p className="text-sm text-violet-700 dark:text-violet-300">
                <strong>⭐ "Rental Saya"</strong> memiliki skeleton dengan ukuran terpanjang di semua breakpoint,
                sesuai dengan jumlah karakter text aslinya (11 karakter).
              </p>
            </div>
          </div>
        </section>

        {/* Live Comparison Section */}
        <section className="mb-12">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
            Live Comparison: Skeleton vs Actual Text
          </h2>
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="grid md:grid-cols-2 gap-8">
              {/* Skeleton Column */}
              <div>
                <h3 className="text-lg font-medium text-gray-700 dark:text-gray-300 mb-4">
                  Skeleton Loading
                </h3>
                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <div className="w-5 h-5 bg-gray-200 dark:bg-gray-600 rounded animate-pulse"></div>
                    <BerandaSkeleton variant="desktop" />
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-5 h-5 bg-gray-200 dark:bg-gray-600 rounded animate-pulse"></div>
                    <KatalogSkeleton variant="desktop" />
                  </div>
                  <div className="flex items-center space-x-3 p-2 bg-violet-50 dark:bg-violet-900/20 rounded border border-violet-200 dark:border-violet-700">
                    <div className="w-5 h-5 bg-violet-200 dark:bg-violet-600 rounded animate-pulse"></div>
                    <RentalSayaSkeleton variant="desktop" />
                    <span className="text-xs text-violet-600 font-bold">← TERPANJANG</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-5 h-5 bg-gray-200 dark:bg-gray-600 rounded animate-pulse"></div>
                    <ProfilSkeleton variant="desktop" />
                  </div>
                </div>
              </div>

              {/* Actual Text Column */}
              <div>
                <h3 className="text-lg font-medium text-gray-700 dark:text-gray-300 mb-4">
                  Actual Navigation Text
                </h3>
                <div className="space-y-3">
                  <div className="flex items-center space-x-3 px-4 py-2.5 rounded-lg bg-gray-50 dark:bg-gray-700">
                    <div className="w-5 h-5 bg-blue-500 rounded"></div>
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Beranda</span>
                  </div>
                  <div className="flex items-center space-x-3 px-4 py-2.5 rounded-lg bg-gray-50 dark:bg-gray-700">
                    <div className="w-5 h-5 bg-green-500 rounded"></div>
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Katalog</span>
                  </div>
                  <div className="flex items-center space-x-3 px-4 py-2.5 rounded-lg bg-violet-100 dark:bg-violet-900/30 border border-violet-200 dark:border-violet-700">
                    <div className="w-5 h-5 bg-violet-500 rounded"></div>
                    <span className="text-sm font-medium text-violet-700 dark:text-violet-300 font-bold">Rental Saya</span>
                    <span className="text-xs text-violet-600 font-bold">← TERPANJANG</span>
                  </div>
                  <div className="flex items-center space-x-3 px-4 py-2.5 rounded-lg bg-gray-50 dark:bg-gray-700">
                    <div className="w-5 h-5 bg-purple-500 rounded"></div>
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Profil</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-6 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-700">
              <h4 className="text-sm font-medium text-green-800 dark:text-green-300 mb-2">
                ✅ Perfect Match!
              </h4>
              <p className="text-sm text-green-700 dark:text-green-400">
                Skeleton "Rental Saya" memiliki ukuran yang sama dengan text aslinya, memberikan preview yang akurat dan mengurangi layout shift.
              </p>
            </div>
          </div>
        </section>

        {/* Instructions Section */}
        <section className="mb-12">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
            Cara Melihat Skeleton Loading
          </h2>
          <div className="bg-blue-50 dark:bg-blue-900/20 rounded-xl p-6 border border-blue-200 dark:border-blue-700">
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">1</div>
                <div>
                  <h3 className="font-medium text-blue-800 dark:text-blue-300">Refresh Halaman Dashboard</h3>
                  <p className="text-sm text-blue-700 dark:text-blue-400">
                    Kembali ke halaman dashboard utama dan refresh (F5) untuk melihat skeleton loading selama 1 detik.
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">2</div>
                <div>
                  <h3 className="font-medium text-blue-800 dark:text-blue-300">Perhatikan Sidebar Navigation</h3>
                  <p className="text-sm text-blue-700 dark:text-blue-400">
                    Lihat skeleton loading di sidebar kiri - "Rental Saya" akan memiliki skeleton terpanjang.
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">3</div>
                <div>
                  <h3 className="font-medium text-blue-800 dark:text-blue-300">Test di Mobile</h3>
                  <p className="text-sm text-blue-700 dark:text-blue-400">
                    Buka developer tools (F12) dan switch ke mobile view untuk melihat mobile bottom navigation skeleton.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
}
