"use client";

import { 
  RealisticNavigationSkeleton,
  BerandaSkeleton,
  KatalogSkeleton,
  RentalSayaSkeleton,
  ProfilSkeleton,
  MobileMenuSkeleton
} from "./nav-skeleton";
import { MobileBottomNavigationSkeleton } from "../shared/mobile-bottom-navigation-skeleton";
import { cn } from "@/lib/utils/cn";

// Demo component to show skeleton size differences
export function SkeletonSizeDemo() {
  return (
    <div className="p-8 space-y-12 bg-gray-50 dark:bg-gray-900 min-h-screen">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
          Skeleton Loading Demo
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mb-8">
          Demonstrasi ukuran skeleton yang realistis untuk setiap item navigasi
        </p>

        {/* Desktop Navigation Skeleton */}
        <section className="mb-12">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
            Desktop Navigation Skeleton
          </h2>
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <RealisticNavigationSkeleton variant="desktop" />
            
            {/* Individual Items for Comparison */}
            <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
              <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-4">
                Individual Items (Desktop):
              </h3>
              <div className="flex items-center space-x-2">
                <div className="text-center">
                  <BerandaSkeleton variant="desktop" />
                  <p className="text-xs text-gray-500 mt-2">Beranda (w-20)</p>
                </div>
                <div className="text-center">
                  <KatalogSkeleton variant="desktop" />
                  <p className="text-xs text-gray-500 mt-2">Katalog (w-16)</p>
                </div>
                <div className="text-center">
                  <RentalSayaSkeleton variant="desktop" />
                  <p className="text-xs text-gray-500 mt-2 font-bold text-violet-600">
                    Rental Saya (w-24) ⭐
                  </p>
                </div>
                <div className="text-center">
                  <ProfilSkeleton variant="desktop" />
                  <p className="text-xs text-gray-500 mt-2">Profil (w-16)</p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Tablet Navigation Skeleton */}
        <section className="mb-12">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
            Tablet Navigation Skeleton
          </h2>
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <RealisticNavigationSkeleton variant="tablet" />
            
            <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
              <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-4">
                Individual Items (Tablet):
              </h3>
              <div className="flex items-center space-x-2">
                <div className="text-center">
                  <BerandaSkeleton variant="tablet" />
                  <p className="text-xs text-gray-500 mt-2">Beranda (w-14)</p>
                </div>
                <div className="text-center">
                  <KatalogSkeleton variant="tablet" />
                  <p className="text-xs text-gray-500 mt-2">Katalog (w-12)</p>
                </div>
                <div className="text-center">
                  <RentalSayaSkeleton variant="tablet" />
                  <p className="text-xs text-gray-500 mt-2 font-bold text-violet-600">
                    Rental Saya (w-18) ⭐
                  </p>
                </div>
                <div className="text-center">
                  <ProfilSkeleton variant="tablet" />
                  <p className="text-xs text-gray-500 mt-2">Profil (w-10)</p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Mobile Navigation Skeleton */}
        <section className="mb-12">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
            Mobile Navigation Skeleton
          </h2>
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Mobile Menu Button:
              </h3>
              <RealisticNavigationSkeleton variant="mobile" />
            </div>

            <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
              <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-4">
                Individual Items (Mobile):
              </h3>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <BerandaSkeleton variant="mobile" />
                  <p className="text-xs text-gray-500">Beranda (w-16)</p>
                </div>
                <div className="flex items-center justify-between">
                  <KatalogSkeleton variant="mobile" />
                  <p className="text-xs text-gray-500">Katalog (w-14)</p>
                </div>
                <div className="flex items-center justify-between">
                  <RentalSayaSkeleton variant="mobile" />
                  <p className="text-xs text-gray-500 font-bold text-violet-600">
                    Rental Saya (w-20) ⭐ TERPANJANG
                  </p>
                </div>
                <div className="flex items-center justify-between">
                  <ProfilSkeleton variant="mobile" />
                  <p className="text-xs text-gray-500">Profil (w-12)</p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Mobile Bottom Navigation Skeleton */}
        <section className="mb-12">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
            Mobile Bottom Navigation Skeleton
          </h2>
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="relative">
              <MobileBottomNavigationSkeleton />
              <div className="h-20"></div> {/* Spacer for fixed positioning */}
            </div>
            
            <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
              <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-4">
                Bottom Navigation Items:
              </h3>
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <p className="text-xs text-gray-500">Beranda (w-12)</p>
                </div>
                <div>
                  <p className="text-xs text-gray-500">Katalog (w-14)</p>
                </div>
                <div>
                  <p className="text-xs text-gray-500">Rental (w-12)</p>
                  <p className="text-xs text-gray-400">(shortened)</p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Size Comparison Chart */}
        <section className="mb-12">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
            Size Comparison Chart
          </h2>
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b border-gray-200 dark:border-gray-700">
                    <th className="text-left py-2 text-gray-600 dark:text-gray-400">Item</th>
                    <th className="text-center py-2 text-gray-600 dark:text-gray-400">Desktop</th>
                    <th className="text-center py-2 text-gray-600 dark:text-gray-400">Tablet</th>
                    <th className="text-center py-2 text-gray-600 dark:text-gray-400">Mobile</th>
                    <th className="text-center py-2 text-gray-600 dark:text-gray-400">Karakter</th>
                  </tr>
                </thead>
                <tbody className="text-gray-700 dark:text-gray-300">
                  <tr className="border-b border-gray-100 dark:border-gray-700">
                    <td className="py-2">Beranda</td>
                    <td className="text-center py-2">w-20 (80px)</td>
                    <td className="text-center py-2">w-14 (56px)</td>
                    <td className="text-center py-2">w-16 (64px)</td>
                    <td className="text-center py-2">7</td>
                  </tr>
                  <tr className="border-b border-gray-100 dark:border-gray-700">
                    <td className="py-2">Katalog</td>
                    <td className="text-center py-2">w-16 (64px)</td>
                    <td className="text-center py-2">w-12 (48px)</td>
                    <td className="text-center py-2">w-14 (56px)</td>
                    <td className="text-center py-2">7</td>
                  </tr>
                  <tr className="border-b border-gray-100 dark:border-gray-700 bg-violet-50 dark:bg-violet-900/20">
                    <td className="py-2 font-bold text-violet-600 dark:text-violet-400">Rental Saya ⭐</td>
                    <td className="text-center py-2 font-bold text-violet-600 dark:text-violet-400">w-24 (96px)</td>
                    <td className="text-center py-2 font-bold text-violet-600 dark:text-violet-400">w-18 (72px)</td>
                    <td className="text-center py-2 font-bold text-violet-600 dark:text-violet-400">w-20 (80px)</td>
                    <td className="text-center py-2 font-bold text-violet-600 dark:text-violet-400">11</td>
                  </tr>
                  <tr>
                    <td className="py-2">Profil</td>
                    <td className="text-center py-2">w-16 (64px)</td>
                    <td className="text-center py-2">w-10 (40px)</td>
                    <td className="text-center py-2">w-12 (48px)</td>
                    <td className="text-center py-2">6</td>
                  </tr>
                </tbody>
              </table>
            </div>
            
            <div className="mt-4 p-4 bg-violet-50 dark:bg-violet-900/20 rounded-lg">
              <p className="text-sm text-violet-700 dark:text-violet-300">
                <strong>⭐ "Rental Saya"</strong> memiliki skeleton dengan ukuran terpanjang di semua breakpoint, 
                sesuai dengan jumlah karakter text aslinya (11 karakter).
              </p>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
}
