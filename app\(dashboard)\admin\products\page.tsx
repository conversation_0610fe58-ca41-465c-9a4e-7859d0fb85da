import { Metadata } from "next";
import { prisma } from "@/lib/config/prisma";
import { ProductTable } from "@/app/components/admin/product-table";
import { Suspense } from "react";
import { LoadingState } from "@/app/components/shared/loading-state";
import { auth } from "@/auth";
import { redirect } from "next/navigation";

export const metadata: Metadata = {
  title: "Manajemen Produk | Rental Ganset",
  description: "Kelola produk genset Anda",
};

export const dynamic = 'force-dynamic';
export const revalidate = 0;

interface ProductsPageProps {
  searchParams: Promise<{
    page?: string;
    search?: string;
  }>;
}

async function ProductsContent({ currentPage, searchQuery }: { currentPage: number; searchQuery: string }) {
  try {
    const session = await auth();
    if (!session?.user || session.user.role !== "ADMIN") {
      redirect("/login");
    }

    const itemsPerPage = 5;
    const skip = (currentPage - 1) * itemsPerPage;

    // Build where clause for search
    const whereClause = searchQuery ? {
      OR: [
        { name: { contains: searchQuery, mode: 'insensitive' as const } },
        { description: { contains: searchQuery, mode: 'insensitive' as const } },
        { category: { contains: searchQuery, mode: 'insensitive' as const } },
      ]
    } : {};

    // Fetch products data with pagination
    const [products, totalProducts] = await Promise.all([
      prisma.product.findMany({
        where: whereClause,
        include: {
          user: true,
        },
        orderBy: {
          createdAt: "desc"
        },
        skip,
        take: itemsPerPage,
      }),
      prisma.product.count({
        where: whereClause,
      })
    ]);

    const totalPages = Math.ceil(totalProducts / itemsPerPage);

    console.log('Products found in admin:', products.length, 'of', totalProducts);

    return (
      <>


        <div className="mt-5">
          <ProductTable
            products={products}
            currentPage={currentPage}
            totalPages={totalPages}
            totalProducts={totalProducts}
            searchQuery={searchQuery}
          />
        </div>
      </>
    );
  } catch (error) {
    console.error("Error loading products:", error);
    throw new Error("Gagal memuat data produk");
  }
}

export default async function AdminProductsPage({ searchParams }: ProductsPageProps) {
  const params = await searchParams;
  const currentPage = parseInt(params.page || '1', 10);
  const searchQuery = params.search || '';

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <Suspense fallback={<LoadingState />}>
        <ProductsContent currentPage={currentPage} searchQuery={searchQuery} />
      </Suspense>
    </div>
  );
}
