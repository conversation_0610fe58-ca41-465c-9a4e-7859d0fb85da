import { useEffect, useState } from "react";

export function useDebounce<T>(value: T, delay: number): T {
    const [debouncedValue, setDebouncedValue] = useState<T>(value);

    useEffect(() => {
        const timer = setTimeout(() => {
            setDebouncedValue(value);
        }, delay);

        return () => {
            clearTimeout(timer);
        };
    }, [value, delay]);

    return debouncedValue;
}

export * from "./use-toast";
export { useProductData } from "./product/use-product-data";
export { useRentalForm } from "./rental/use-rental-form";
export { default as useGeoLocation } from './useGeoLocation';
export { default as useMapSearch } from './useMapSearch';
export { default as useReverseGeocode } from './useReverseGeocode'; 
