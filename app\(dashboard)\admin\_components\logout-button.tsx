"use client";

import { But<PERSON> } from "@/app/components/ui/button";
import { signOut } from "next-auth/react";
import { IoPower } from "react-icons/io5";

export function LogoutButton() {
  const handleLogout = async () => {
    await signOut({ callbackUrl: "/" });
  };

  return (
    <Button
      variant="outline"
      size="sm"
      onClick={handleLogout}
      className="flex items-center gap-2"
    >
      <IoPower className="h-4 w-4" />
      Logout
    </Button>
  );
}