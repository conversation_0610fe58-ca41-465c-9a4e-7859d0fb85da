import { Role } from "@/lib/types/auth";

declare module "@auth/core/types" {
    interface User {
        id?: string;
        name?: string | null;
        email?: string | null;
        image?: string | null;
        role?: Role;
        phone?: string | null;
        createdAt?: Date;
        emailVerified?: Date | null;
        password?: string;
    }

    interface Session {
        user: {
            id: string;
            name?: string | null;
            email?: string | null;
            image?: string | null;
            role: Role;
            phone: string | null;
            createdAt: Date;
        };
    }
}

declare module "@auth/core/jwt" {
    interface JWT {
        id: string;
        role: Role;
        phone: string | null;
        createdAt: Date;
        name?: string | null;
        email?: string | null;
        picture?: string | null;
    }
}

declare module "next-auth" {
    interface User {
        id: string;
        role: Role;
        phone: string | null;
        createdAt: Date;
        name?: string | null;
        email?: string | null;
        image?: string | null;
    }
}

declare module "next-auth/jwt" {
    interface JWT {
        id: string;
        role: Role;
        phone: string | null;
        createdAt: Date;
    }
} 
