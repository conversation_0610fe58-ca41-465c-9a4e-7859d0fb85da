import { Metadata } from "next";
import { auth } from "@/auth";
import { redirect } from "next/navigation";
import Link from "next/link";
import { Button } from "@/app/components/ui/button";
import { Badge } from "@/app/components/ui/badge";
import {
  LuCalendar,
  LuCheck,
  LuX,
  LuClock,
  LuCreditCard,
  LuDollarSign,
  LuShoppingCart,
  LuPackage,
  LuCheckCheck,
  LuInfo,
  LuChevronLeft,
  LuChevronRight
} from "react-icons/lu";
import { formatCurrency } from "@/lib/utils/format";
import { prisma } from "@/lib/config/prisma";
import { InvoiceDownloadButton, InvoiceDownloadBadge } from "@/app/components/payment";

export const metadata: Metadata = {
  title: "Pembayaran Saya",
  description: "Daftar pembayaran penyewaan genset Anda",
};

// Jumlah item per halaman untuk pagination
const ITEMS_PER_PAGE = 5;

export default async function PaymentsPage({
  searchParams,
}: {
  searchParams: Promise<{ page?: string }>;
}) {
  const session = await auth();
  if (!session?.user) {
    redirect('/login');
  }

  // Ambil nomor halaman dari query params (default: 1)
  const params = await searchParams;
  const currentPage = Number(params.page || '1');
  const skip = (currentPage - 1) * ITEMS_PER_PAGE;

  // Ambil total jumlah pembayaran untuk pagination
  const totalPayments = await prisma.payment.count({
    where: {
      userId: session.user.id
    }
  });

  const totalPages = Math.ceil(totalPayments / ITEMS_PER_PAGE);

  // Ambil data pembayaran dari database dengan pagination dan select spesifik
  const payments = await prisma.payment.findMany({
    where: {
      userId: session.user.id
    },
    include: {
      rental: {
        select: {
          id: true,
          amount: true,
          operationalEnd: true,
          product: {
            select: {
              name: true
            }
          }
        }
      }
    },
    orderBy: {
      createdAt: 'desc'
    },
    skip: skip,
    take: ITEMS_PER_PAGE
  });

  // Fungsi untuk mendapatkan badge status
  function getStatusBadge(status: string) {
    switch (status.toLowerCase()) {
      case "fully_paid":
        return { label: "Lunas", variant: "default" as const, icon: <LuCheck className="h-4 w-4 mr-1 text-green-300" /> };
      case "deposit_paid":
        return { label: "Deposit Dibayar", variant: "success" as const, icon: <LuCheck className="h-4 w-4 mr-1 text-green-300" /> };
      case "deposit_pending":
        return { label: "Menunggu Deposit", variant: "secondary" as const, icon: <LuCalendar className="h-4 w-4 mr-1 text-yellow-300" /> };
      case "failed":
        return { label: "Gagal", variant: "destructive" as const, icon: <LuX className="h-4 w-4 mr-1" /> };
      default:
        return { label: "Menunggu", variant: "outline" as const, icon: <LuCalendar className="h-4 w-4 mr-1" /> };
    }
  }

  return (
    <div>
      <div className="relative bg-gradient-to-r from-violet-50 to-indigo-50 dark:from-violet-950/40 dark:to-indigo-950/40 rounded-xl mb-8 p-6 shadow-sm border border-gray-100 dark:border-gray-800 overflow-hidden">
        <div className="absolute right-0 top-0 bottom-0 w-1/3 opacity-10 bg-contain bg-right bg-no-repeat" style={{ backgroundImage: "url('/images/payment-pattern.svg')" }}></div>
        <div className="relative">
          <h1 className="text-3xl font-bold tracking-tight text-white">Pembayaran Saya</h1>
          <p className="text-white max-w-xl mt-2">
            Kelola pembayaran dan lihat status transaksi penyewaan genset Anda
          </p>
          <div className="flex flex-wrap items-center gap-4 mt-4">
            <Link href="/user/catalog">
              <Button variant="gradient" size="mobile">
                <LuShoppingCart className="mr-2 h-4 w-4" /> Sewa Genset Baru
              </Button>
            </Link>
            <div className="flex items-center gap-2 text-sm text-white">
              <div className="w-2 h-2 rounded-full bg-green-500"></div>
              <span className="font-medium text-white">Lunas</span>
              <div className="w-2 h-2 rounded-full bg-yellow-500 ml-3"></div>
              <span>Menunggu Pembayaran</span>
            </div>
          </div>
        </div>
      </div>

      {payments.length === 0 ? (
        <div className="flex flex-col items-center justify-center py-16 bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 text-center">
          <div className="w-20 h-20 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center mb-4">
            <LuCreditCard className="h-10 w-10 text-gray-400 dark:text-gray-500" />
          </div>
          <h3 className="text-xl font-medium text-gray-900 dark:text-white mb-2">Belum Ada Riwayat Pembayaran</h3>
          <p className="text-gray-600 dark:text-gray-400 max-w-md mb-6">Anda belum memiliki riwayat pembayaran. Mulailah menyewa genset dan kelola pembayaran Anda di sini.</p>
          <Link href="/user/catalog">
            <Button variant="gradient" size="mobile">
              <LuPackage className="mr-2 h-4 w-4" />
              Lihat Katalog Genset
            </Button>
          </Link>
        </div>
      ) : (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-5 relative overflow-hidden">
              <div className="flex items-center gap-4">
                <div className="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-lg">
                  <LuDollarSign className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">Proses Pembayaran</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Deposit 50% di awal</p>
                </div>
              </div>
              <p className="mt-4 text-gray-700 dark:text-gray-300 text-sm">
                Bayar deposit 50% saat memesan untuk mengamankan penyewaan Anda
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-5 relative overflow-hidden">
              <div className="flex items-center gap-4">
                <div className="bg-green-100 dark:bg-green-900/30 p-3 rounded-lg">
                  <LuCheckCheck className="h-6 w-6 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">Pelunasan</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Sisanya setelah selesai</p>
                </div>
              </div>
              <p className="mt-4 text-gray-700 dark:text-gray-300 text-sm">
                Bayar sisa 50% dan biaya tambahan (jika ada) setelah operasi selesai
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-5 relative overflow-hidden">
              <div className="flex items-center gap-4">
                <div className="bg-orange-100 dark:bg-orange-900/30 p-3 rounded-lg">
                  <LuClock className="h-6 w-6 text-orange-600 dark:text-orange-400" />
                </div>
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">Biaya Overtime</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Jika melebihi durasi</p>
                </div>
              </div>
              <p className="mt-4 text-gray-700 dark:text-gray-300 text-sm">
                Biaya overtime dihitung berdasarkan durasi penggunaan tambahan
              </p>
            </div>
          </div>

          <div className="mb-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Riwayat Pembayaran</h2>
              {session?.user && <InvoiceDownloadBadge userId={session.user.id} />}
            </div>
          </div>

          {payments.map((payment) => {
            const statusBadge = getStatusBadge(payment.status);
            const rental = payment.rental;
            const hasOvertime = payment.overtime && payment.overtime > 0;
            const isCompleted = rental.operationalEnd != null;
            // Tampilkan tombol lunasi jika deposit sudah dibayar dan belum lunas
            const isPending = payment.status === "DEPOSIT_PAID";

            return (
              <div key={payment.id} className={`bg-white dark:bg-gray-800 rounded-xl shadow-sm border ${isPending ? "border-blue-300 dark:border-blue-700" : "border-gray-100 dark:border-gray-700"} overflow-hidden mb-6 transition-all duration-200 hover:shadow-md`}>
                <div className={`px-6 py-5 border-b ${isPending ? "bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800" : "border-gray-100 dark:border-gray-700"}`}>
                  <div className="flex flex-wrap justify-between items-center gap-4">
                    <div className="flex items-center gap-4">
                      <div className={`rounded-lg p-3 ${payment.status === "FULLY_PAID" ? "bg-green-100 dark:bg-green-900/30" : payment.status === "DEPOSIT_PAID" ? "bg-blue-100 dark:bg-blue-900/30" : "bg-yellow-100 dark:bg-yellow-900/30"}`}>
                        {payment.status === "FULLY_PAID" ? (
                          <LuCheckCheck className="h-6 w-6 text-green-600 dark:text-green-400" />
                        ) : payment.status === "DEPOSIT_PAID" ? (
                          <LuCreditCard className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                        ) : (
                          <LuClock className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
                        )}
                      </div>
                      <div>
                        <p className="text-sm text-gray-500 dark:text-gray-400">Kode Pembayaran</p>
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">#{payment.id.substring(0, 8).toUpperCase()}</h3>
                      </div>
                    </div>
                    <Badge variant={statusBadge.variant} className="flex items-center px-3 py-1.5 text-sm">
                      {statusBadge.icon}
                      {statusBadge.label}
                    </Badge>
                  </div>
                </div>

                <div className="p-6">
                  <div className="flex flex-wrap items-center gap-3 mb-6">
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white">{rental.product.name}</h3>
                    <div className="bg-gray-100 dark:bg-gray-700 px-3 py-1 rounded-full text-sm font-medium text-gray-700 dark:text-gray-300">
                      {formatCurrency(rental.amount)}
                    </div>
                    {rental.operationalEnd ? (
                      <Badge variant="outline" className="ml-auto bg-green-50 text-green-700 border-green-200 dark:bg-green-900/30 dark:text-green-300 dark:border-green-800">
                        <LuCheckCheck className="mr-1 h-3 w-3" /> Operasi Selesai
                      </Badge>
                    ) : (
                      <Badge variant="outline" className="ml-auto bg-yellow-50 text-yellow-700 border-yellow-200 dark:bg-yellow-900/30 dark:text-yellow-300 dark:border-yellow-800">
                        <LuClock className="mr-1 h-3 w-3" /> Operasi Berjalan
                      </Badge>
                    )}
                  </div>

                  {/* Payment progress */}
                  <div className="mb-6">
                    <div className="flex justify-between text-sm mb-2">
                      <span className="text-gray-600 dark:text-gray-400">Progress Pembayaran</span>
                      <span className="font-medium text-gray-900 dark:text-white">
                        {payment.status === "FULLY_PAID" ? "100%" : payment.status === "DEPOSIT_PAID" ? "50%" : "0%"}
                      </span>
                    </div>
                    <div className="h-2 bg-gray-100 dark:bg-gray-700 rounded-full overflow-hidden">
                      <div
                        className={`h-full rounded-full ${payment.status === "FULLY_PAID" ? "bg-green-500" : "bg-blue-500"}`}
                        style={{ width: payment.status === "FULLY_PAID" ? "100%" : payment.status === "DEPOSIT_PAID" ? "50%" : "0%" }}
                      ></div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <div className="p-4 rounded-lg bg-gray-50 dark:bg-gray-800 border border-gray-100 dark:border-gray-700">
                      <p className="text-sm text-gray-500 dark:text-gray-400 mb-1">Deposit (50%)</p>
                      <p className="text-lg font-semibold text-gray-900 dark:text-white">{formatCurrency(payment.deposit)}</p>
                      <div className="mt-2 flex items-center text-xs">
                        {payment.status !== "DEPOSIT_PENDING" ? (
                          <span className="text-green-600 dark:text-green-400 flex items-center">
                            <LuCheck className="mr-1 h-3 w-3" /> Dibayar
                          </span>
                        ) : (
                          <span className="text-yellow-600 dark:text-yellow-400 flex items-center">
                            <LuClock className="mr-1 h-3 w-3" /> Menunggu
                          </span>
                        )}
                      </div>
                    </div>

                    <div className="p-4 rounded-lg bg-gray-50 dark:bg-gray-800 border border-gray-100 dark:border-gray-700">
                      <p className="text-sm text-gray-500 dark:text-gray-400 mb-1">Pelunasan (50%)</p>
                      <p className="text-lg font-semibold text-gray-900 dark:text-white">{formatCurrency(payment.remaining)}</p>
                      <div className="mt-2 flex items-center text-xs">
                        {payment.status === "FULLY_PAID" ? (
                          <span className="text-green-600 dark:text-green-400 flex items-center">
                            <LuCheck className="mr-1 h-3 w-3" /> Dibayar
                          </span>
                        ) : (
                          <span className="text-yellow-600 dark:text-yellow-400 flex items-center">
                            <LuClock className="mr-1 h-3 w-3" /> {payment.status === "DEPOSIT_PENDING" ? "Setelah deposit" : "Menunggu dibayar"}
                          </span>
                        )}
                      </div>
                    </div>

                    <div className="p-4 rounded-lg bg-gray-50 dark:bg-gray-800 border border-gray-100 dark:border-gray-700">
                      <p className="text-sm text-gray-500 dark:text-gray-400 mb-1">
                        {hasOvertime ? "Biaya Overtime" : "Status Biaya Overtime"}
                      </p>
                      {hasOvertime ? (
                        <p className="text-lg font-semibold text-red-600 dark:text-red-400">{formatCurrency(payment.overtime || 0)}</p>
                      ) : (
                        <p className="text-lg font-semibold text-gray-900 dark:text-white">Rp 0</p>
                      )}
                      <div className="mt-2 flex items-center text-xs">
                        {isCompleted ? (
                          hasOvertime ? (
                            <span className="text-red-600 dark:text-red-400 flex items-center">
                              <LuInfo className="mr-1 h-3 w-3" /> Termasuk di pelunasan
                            </span>
                          ) : (
                            <span className="text-green-600 dark:text-green-400 flex items-center">
                              <LuCheck className="mr-1 h-3 w-3" /> Tidak ada overtime
                            </span>
                          )
                        ) : (
                          <span className="text-gray-500 dark:text-gray-400 flex items-center">
                            <LuClock className="mr-1 h-3 w-3" /> Menunggu operasi selesai
                          </span>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Total yang harus dibayar */}
                  {isPending && (
                    <div className="p-4 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg border border-blue-200 dark:border-blue-800 mb-6">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <LuInfo className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                          <span className="font-medium text-blue-800 dark:text-blue-300">Sisa Pembayaran yang Harus Dilunasi:</span>
                        </div>
                        <span className="text-xl font-bold text-blue-800 dark:text-blue-300">
                          {formatCurrency(payment.remaining + (payment.overtime || 0))}
                        </span>
                      </div>
                      {hasOvertime && (
                        <p className="mt-2 text-sm text-blue-700 dark:text-blue-400 pl-7">
                          *Termasuk biaya overtime sebesar {formatCurrency(payment.overtime || 0)}
                        </p>
                      )}
                      <div className="mt-3 p-3 bg-white/50 dark:bg-gray-800/50 rounded-lg">
                        <p className="text-sm text-blue-700 dark:text-blue-300 font-medium">
                          💡 Klik tombol &quot;Lunasi Pembayaran&quot; di bawah untuk melanjutkan pembayaran
                        </p>
                      </div>
                    </div>
                  )}
                </div>

                <div className="px-6 py-6 border-t border-gray-100 dark:border-gray-700 bg-gradient-to-r from-gray-50/50 to-blue-50/30 dark:from-gray-800/50 dark:to-blue-900/20">
                  <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                    <div className="flex flex-col gap-1">
                      <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        ID Transaksi
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400 font-mono bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
                        {payment.id.substring(0, 12)}...
                      </div>
                    </div>
                    <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto">
                      {payment.status === "DEPOSIT_PENDING" && (
                        <Link href={`/user/payments/deposit/${rental.id}`}>
                          <Button className="bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600 dark:from-green-500/90 dark:to-green-600/90 dark:hover:from-green-500 dark:hover:to-green-700 text-white dark:text-white shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 min-h-[44px] rounded-xl font-medium w-full sm:w-auto">
                            <LuCreditCard className="mr-2 h-4 w-4" />
                            Bayar Deposit Sekarang
                          </Button>
                        </Link>
                      )}

                      {isPending && (
                        <Link href={`/user/payments/remaining/${rental.id}`}>
                          <Button variant="gradient" size="mobile" className="w-full sm:w-auto">
                            <LuDollarSign className="mr-2 h-4 w-4" />
                            Lunasi Pembayaran
                          </Button>
                        </Link>
                      )}

                      {payment.status !== "DEPOSIT_PENDING" ? (
                        <InvoiceDownloadButton
                          invoiceId={rental.id}
                          label="Download Invoice"
                          variant="default"
                        />
                      ) : (
                        <div className="text-sm text-gray-500 dark:text-gray-400 italic px-4 py-2 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                          📄 Invoice tersedia setelah deposit dibayar
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            );
          })}

          {/* Pagination controls */}
          {totalPages > 1 && (
            <div className="flex justify-center items-center gap-2 mt-8">
              <Link href={`/user/payments?page=${Math.max(currentPage - 1, 1)}`}>
                <Button
                  variant="outline"
                  className="border-gray-200 dark:border-gray-700 dark:text-gray-300 dark:hover:bg-gray-800 dark:hover:text-white"
                  disabled={currentPage === 1}
                >
                  <LuChevronLeft className="h-4 w-4" />
                </Button>
              </Link>

              <div className="flex items-center gap-1">
                {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                  <Link key={page} href={`/user/payments?page=${page}`}>
                    <Button
                      variant={currentPage === page ? "violet" : "outline"}
                      size="icon"
                    >
                      {page}
                    </Button>
                  </Link>
                ))}
              </div>

              <Link href={`/user/payments?page=${Math.min(currentPage + 1, totalPages)}`}>
                <Button
                  variant="outline"
                  className="border-gray-200 dark:border-gray-700 dark:text-gray-300 dark:hover:bg-gray-800 dark:hover:text-white"
                  disabled={currentPage === totalPages}
                >
                  <LuChevronRight className="h-4 w-4" />
                </Button>
              </Link>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
