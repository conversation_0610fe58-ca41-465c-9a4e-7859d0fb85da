"use client";

import { useState, useEffect } from "react";
import { Bell } from "lucide-react";
import { Button } from "../../ui/button";
import { Badge } from "../../ui/badge";

/**
 * Komponen sederhana untuk menampilkan jumlah notifikasi
 * Digunakan untuk tampilan yang lebih kecil atau di dalam komponen lain
 */
export function NotificationCounter() {
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchNotificationCount = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/notifications/counts', {
          credentials: 'include',
        });
        
        if (response.ok) {
          const data = await response.json();
          setUnreadCount(data.total || 0);
        }
      } catch (error) {
        console.error("Error fetching notification count:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchNotificationCount();
    
    // Polling untuk update jumlah notifikasi
    const intervalId = setInterval(fetchNotificationCount, 60000); // Setiap 1 menit
    
    return () => clearInterval(intervalId);
  }, []);

  if (loading || unreadCount === 0) {
    return (
      <Button variant="ghost" size="icon" className="relative">
        <Bell className="h-5 w-5" />
      </Button>
    );
  }

  return (
    <Button variant="ghost" size="icon" className="relative">
      <Bell className="h-5 w-5" />
      <Badge className="absolute -top-1 -right-1 px-1.5 py-0.5 min-w-4 h-4 flex items-center justify-center text-[10px] bg-red-500">
        {unreadCount > 9 ? '9+' : unreadCount}
      </Badge>
    </Button>
  );
} 
