import { auth } from "@/auth";
import { prisma } from "@/lib/config/prisma";
import { NextResponse } from "next/server";
import MidtransService from "@/lib/services/midtrans";

export const runtime = 'nodejs';

// POST /api/payments/deposit/[rentalId] - Create deposit payment
export async function POST(
  request: Request,
  { params }: { params: Promise<{ rentalId: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { rentalId } = await params;
    
    const rental = await prisma.rental.findUnique({
      where: { id: rentalId },
      include: { 
        payment: true,
        product: true,
        user: true
      }
    });

    if (!rental) {
      return NextResponse.json({ error: "Rental tidak ditemukan" }, { status: 404 });
    }

    // Pastikan user yang login adalah pemilik rental
    if (rental.userId !== session.user.id) {
      return NextResponse.json({ error: "Tidak memiliki akses ke rental ini" }, { status: 403 });
    }

    // Cek apakah deposit sudah dibayar
    if (rental.payment && rental.payment.status !== 'DEPOSIT_PENDING') {
      return NextResponse.json({ error: "Deposit sudah dibayar sebelumnya" }, { status: 400 });
    }

    const depositAmount = Math.floor(rental.amount * 0.5);

    // Buat token pembayaran Midtrans
    const payment = await MidtransService.createPayment({
      orderId: `${rental.id}_deposit_${Date.now()}`,
      amount: depositAmount,
      email: session.user.email || "",
      name: session.user.name || "Customer",
      productName: `Deposit Rental Genset - ${rental.product.name}`
    });

    // Update atau buat record payment
    await prisma.payment.upsert({
      where: { rentalId: rental.id },
      update: {
        deposit: depositAmount,
        snapToken: payment.token,
        updatedAt: new Date()
      },
      create: {
        rentalId: rental.id,
        amount: rental.amount,
        deposit: depositAmount,
        remaining: rental.amount - depositAmount,
        snapToken: payment.token,
        status: 'DEPOSIT_PENDING',
        userId: session.user.id
      }
    });

    return NextResponse.json({ 
      success: true,
      token: payment.token,
      message: "Token pembayaran berhasil dibuat"
    });

  } catch (error) {
    console.error("[DEPOSIT_PAYMENT_ERROR]", error);
    return NextResponse.json(
      { error: "Gagal memproses pembayaran deposit" },
      { status: 500 }
    );
  }
}
