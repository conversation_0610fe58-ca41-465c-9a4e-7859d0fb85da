import { PaymentRequest, MidtransNotification, MidtransPaymentResponse } from "@/lib/types/payment";
import midtransClient from "midtrans-client";
import crypto from "crypto";

// Konfigurasi untuk logging
const logger = {
  log: (...args: unknown[]) => {
    if (process.env.NODE_ENV !== 'production') {
      console.log(...args);
    }
  },
  error: (...args: unknown[]) => console.error(...args),
  warn: (...args: unknown[]) => console.warn(...args)
};

// Konfigurasi Midtrans
const midtransConfig = {
  isProduction: process.env.NODE_ENV === 'production',
  serverKey: process.env.MIDTRANS_SERVER_KEY!,
  clientKey: process.env.NEXT_PUBLIC_MIDTRANS_CLIENT_KEY!,
};

// Inisialisasi Snap instance
const snap = new midtransClient.Snap({
  isProduction: midtransConfig.isProduction,
  serverKey: midtransConfig.serverKey,
  clientKey: midtransConfig.clientKey,
});

/**
 * Validasi request pembayaran sebelum dikirim ke Midtrans
 */
function validatePaymentRequest(data: PaymentRequest): void {
  if (!data.amount || data.amount <= 0) {
    throw new Error('Invalid amount');
  }

  if (!data.email) {
    throw new Error('Email is required');
  }

  if (!data.name) {
    throw new Error('Name is required');
  }

  if (!data.orderId) {
    throw new Error('Order ID is required');
  }

  if (!data.productName) {
    throw new Error('Product name is required');
  }
}

/**
 * Membuat transaksi pembayaran di Midtrans
 */
export async function createPayment({
  orderId,
  amount,
  name,
  email,
  productName
}: PaymentRequest): Promise<MidtransPaymentResponse> {
  try {
    validatePaymentRequest({ orderId, amount, name, email, productName });

    const parameter = {
      transaction_details: {
        order_id: orderId,
        gross_amount: amount,
      },
      credit_card: {
        secure: true
      },
      enabled_payments: [
        "credit_card",
        "bca_va",
        "bni_va",
        "bri_va",
        "gopay",
        "shopeepay"
      ],
      customer_details: {
        first_name: name,
        email: email,
      },
      item_details: [{
        id: orderId,
        price: amount,
        quantity: 1,
        name: productName
      }],
      callbacks: {
        finish: `${process.env.NEXT_PUBLIC_BASE_URL}/user/payments/callback?order_id=${orderId}&status=success`,
        error: `${process.env.NEXT_PUBLIC_BASE_URL}/user/payments/callback?order_id=${orderId}&status=error`,
        pending: `${process.env.NEXT_PUBLIC_BASE_URL}/user/payments/callback?order_id=${orderId}&status=pending`
      }
    };

    const transaction = await snap.createTransaction(parameter);
    return { token: transaction.token };
  } catch (error) {
    logger.error("Midtrans error:", error);
    throw error;
  }
}

/**
 * Memverifikasi signature notification dari Midtrans
 */
export function verifySignature(notification: MidtransNotification): boolean {
  try {
    const serverKey = midtransConfig.serverKey;
    if (!serverKey) throw new Error('Server key tidak ditemukan');

    const {
      order_id,
      status_code,
      gross_amount,
      signature_key
    } = notification;

    const stringToSign = order_id + status_code + gross_amount + serverKey;
    const expectedSignature = crypto
      .createHash('sha256')
      .update(stringToSign)
      .digest('hex');

    return signature_key === expectedSignature;
  } catch (error) {
    logger.error('Signature verification error:', error);
    return false;
  }
}

// Namespace exports
const MidtransService = {
  createPayment,
  verifySignature,
  config: midtransConfig
};

export default MidtransService;
