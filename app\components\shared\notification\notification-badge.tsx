"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Lu<PERSON>lock, LuCircleAlert, LuDollarSign, LuPackage, LuArchive } from "react-icons/lu";
import { Button } from "../../ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../../ui/dropdown-menu";
import { Badge } from "../../ui/badge";

interface Notification {
  id: string;
  title: string;
  message: string;
  type: string;
  isRead: boolean;
  createdAt: Date;
}

export function NotificationBadge() {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);

  // Jumlah notifikasi yang belum dibaca
  const unreadCount = notifications.filter(n => !n.isRead).length;

  useEffect(() => {
    const fetchNotifications = async () => {
      try {
        setLoading(true);
        const controller = new AbortController();
        const signal = controller.signal;

        const response = await fetch('/api/notifications', {
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json'
          },
          signal,
        }).catch(err => {
          if (err.name === 'AbortError') {
            // Ignore abort errors
            return null;
          }
          console.error("Network error in fetch:", err);
          setError(true);
          return null;
        });

        if (!response) {
          return;
        }

        if (response.status === 401) {
          // Handle unauthorized - clear notifications
          setNotifications([]);
          setError(false);
          return;
        }

        if (!response.ok) {
          console.error("API returned error:", response.status);
          setError(true);
          return;
        }

        const data = await response.json();
        if (!data || !Array.isArray(data.items)) {
          console.error("Invalid response format");
          setError(true);
          return;
        }

        setNotifications(data.items);
        setError(false);
      } catch (error) {
        console.error("Error fetching notifications:", error);
        setError(true);
      } finally {
        setLoading(false);
      }
    };

    // Menggunakan try-catch untuk menghindari crash jika fetch gagal
    try {
      fetchNotifications();
    } catch (e) {
      console.error("Fatal error in notification fetch:", e);
      setError(true);
      setLoading(false);
    }

    // Polling hanya jika tidak ada error
    let intervalId: NodeJS.Timeout | null = null;
    if (!error) {
      intervalId = setInterval(() => {
        try {
          fetchNotifications();
        } catch (e) {
          console.error("Error in notification polling:", e);
        }
      }, 60000); // Polling setiap 1 menit
    }

    return () => {
      // Membersihkan interval polling
      if (intervalId) clearInterval(intervalId);
    };
  }, [error]);

  const markAsRead = async (id: string) => {
    try {
      const response = await fetch(`/api/notifications/${id}/read`, {
        method: 'POST',
        credentials: 'include',
      }).catch(err => {
        console.error("Network error in markAsRead:", err);
        return null;
      });

      if (response && response.ok) {
        // Update state setelah menandai sebagai telah dibaca
        setNotifications(notifications.map(notification =>
          notification.id === id ? { ...notification, isRead: true } : notification
        ));
      }
    } catch (error) {
      console.error("Error marking notification as read:", error);
    }
  };

  const markAllAsRead = async () => {
    try {
      const response = await fetch('/api/notifications/read-all', {
        method: 'POST',
        credentials: 'include',
      }).catch(err => {
        console.error("Network error in markAllAsRead:", err);
        return null;
      });

      if (response && response.ok) {
        // Update state setelah menandai semua sebagai telah dibaca
        setNotifications(notifications.map(notification => ({ ...notification, isRead: true })));
      }
    } catch (error) {
      console.error("Error marking all notifications as read:", error);
    }
  };

  // Helper untuk mendapatkan ikon berdasarkan tipe notifikasi
  const getNotificationIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'payment_success':
      case 'new_payment':
      case 'new_invoice':
        return <LuDollarSign className="h-4 w-4 text-green-500 dark:text-green-400" />;
      case 'operation_started':
      case 'operation_completed':
        return <LuClock className="h-4 w-4 text-blue-500 dark:text-blue-400" />;
      case 'low_stock':
        return <LuPackage className="h-4 w-4 text-orange-500 dark:text-orange-400" />;
      case 'payment_failed':
        return <LuCircleAlert className="h-4 w-4 text-red-500 dark:text-violet-400" />;
      default:
        return <LuArchive className="h-4 w-4 text-gray-500 dark:text-gray-400" />;
    }
  };

  // Format tanggal relatif (misalnya "2 jam yang lalu")
  const formatRelativeTime = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - new Date(date).getTime();

    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days} hari yang lalu`;
    } else if (hours > 0) {
      return `${hours} jam yang lalu`;
    } else if (minutes > 0) {
      return `${minutes} menit yang lalu`;
    } else {
      return 'Baru saja';
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="relative text-muted-foreground hover:text-primary dark:text-muted-foreground dark:hover:text-primary transition-colors"
        >
          <LuBell className="h-5 w-5" />
          {unreadCount > 0 && (
            <Badge className="absolute -top-1 -right-1 px-1.5 py-0.5 min-w-4 h-4 flex items-center justify-center text-[10px] bg-red-600 text-white border-2 border-background shadow-lg">
              {unreadCount > 9 ? '9+' : unreadCount}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-80 p-0 rounded-lg border border-border shadow-lg overflow-hidden bg-popover">
        <div className="bg-muted/50 px-4 py-3 border-b border-border flex items-center justify-between">
          <h3 className="font-medium text-popover-foreground flex items-center">
            <LuBell className="h-4 w-4 mr-2 text-primary" />
            Notifikasi
          </h3>
          {unreadCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              className="text-xs h-7 text-primary hover:bg-primary/10"
              onClick={markAllAsRead}
            >
              <LuCheck className="h-3.5 w-3.5 mr-1" />
              Tandai semua dibaca
            </Button>
          )}
        </div>
        <div className="max-h-[350px] overflow-y-auto py-0.5 bg-popover">
          {loading ? (
            <div className="p-6 text-center text-muted-foreground flex flex-col items-center">
              <div className="h-5 w-5 rounded-full border-2 border-primary border-t-transparent animate-spin mb-3"></div>
              Memuat notifikasi...
            </div>
          ) : error ? (
            <div className="p-6 text-center text-muted-foreground flex flex-col items-center">
              <LuCircleAlert className="h-5 w-5 text-destructive mb-2" />
              Gagal memuat notifikasi
            </div>
          ) : notifications.length === 0 ? (
            <div className="p-6 text-center text-muted-foreground flex flex-col items-center">
              <LuArchive className="h-6 w-6 mb-2 text-muted-foreground" />
              Tidak ada notifikasi
            </div>
          ) : (
            notifications.slice(0, 5).map((notification) => (
              <DropdownMenuItem
                key={notification.id}
                className={`py-3 px-4 cursor-pointer flex items-start gap-3 hover:bg-accent border-b border-border ${!notification.isRead ? 'bg-accent/50' : ''}`}
                onClick={() => markAsRead(notification.id)}
              >
                <div className="p-2 rounded-full bg-primary/10 text-primary shrink-0 mt-0.5">
                  {getNotificationIcon(notification.type)}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex justify-between items-start gap-2">
                    <p className="font-medium text-sm text-popover-foreground">{notification.title}</p>
                    <span className="text-xs text-muted-foreground whitespace-nowrap shrink-0">
                      {formatRelativeTime(new Date(notification.createdAt))}
                    </span>
                  </div>
                  <p className="text-xs text-muted-foreground mt-0.5 line-clamp-2">{notification.message}</p>
                  {!notification.isRead && (
                    <Badge variant="info" className="mt-2">
                      Baru
                    </Badge>
                  )}
                </div>
              </DropdownMenuItem>
            ))
          )}
        </div>
        {notifications.length > 5 && (
          <div className="p-2 border-t border-border bg-popover">
            <Button
              variant="outline"
              size="sm"
              className="w-full text-xs font-medium"
            >
              Lihat semua notifikasi
            </Button>
          </div>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
