import { Metadata } from "next";
import { notFound } from "next/navigation";
import { getProductById } from "@/lib/data/product";
import { BackButton } from "@/app/components/ui/back-button";
import { SoundButton } from "@/app/components/ui/sound-button";
import { formatCurrency } from "@/lib/utils/format";
import { ProductImage } from "@/app/components/product/product-image";
import Link from "next/link";
import { ProductStatus } from "@prisma/client";
import { calculateOvertimeRate } from "@/lib/utils/calculate";
import { Zap, Clock, Gauge, Database } from "lucide-react";

export const metadata: Metadata = {
  title: "Detail Produk",
  description: "Informasi detail produk genset",
};

interface Props {
  params: { id: string };
}

export default async function ProductDetailPage({
  params
}: Props) {
  const { id } = await Promise.resolve(params);

  if (!id) {
    notFound();
  }

  try {
    const product = await getProductById(id);

    if (!product) {
      notFound();
    }

    // Hitung tarif overtime jika tidak tersedia
    const overtimeRate = product.overtimeRate || calculateOvertimeRate(product.capacity);

    return (
      <div className="max-w-7xl mx-auto p-6 space-y-6">
        <BackButton href="/user/catalog" />

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
          <div className="grid md:grid-cols-2 gap-6">
            {/* Gambar Produk */}
            <div className="aspect-[4/3] relative">
              <ProductImage
                imageUrl={product.imageUrl}
                name={product.name}
                size="full"
              />
            </div>

            {/* Informasi Produk */}
            <div className="p-6">
              <div className="mb-6">
                <h1 className="text-2xl font-bold mb-2 dark:text-white">{product.name}</h1>
                <span className={`px-2 py-1 text-xs font-semibold rounded-full ${product.status === ProductStatus.AVAILABLE
                  ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                  : "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
                  }`}>
                  {product.status === ProductStatus.AVAILABLE ? "Tersedia" : "Disewa"}
                </span>
              </div>

              <dl className="space-y-4">
                <div>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 flex items-center gap-1">
                    <Zap className="h-4 w-4 text-yellow-500 dark:text-yellow-400" /> Kapasitas
                  </dt>
                  <dd className="mt-1 text-lg font-semibold text-green-600 dark:text-green-400">{product.capacity} KVA</dd>
                </div>

                <div>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 flex items-center gap-1">
                    <Database className="h-4 w-4 text-blue-500 dark:text-blue-400" /> Harga Sewa per Hari
                  </dt>
                  <dd className="mt-1 text-lg font-semibold text-green-600 dark:text-green-400">
                    {formatCurrency(product.price)}
                  </dd>
                </div>

                <div>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 flex items-center gap-1">
                    <Clock className="h-4 w-4 text-red-500 dark:text-red-400" /> Biaya Overtime per Jam
                  </dt>
                  <dd className="mt-1 text-lg font-semibold text-red-600 dark:text-red-400">
                    {formatCurrency(overtimeRate)}
                  </dd>
                </div>

                <div>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 flex items-center gap-1">
                    <Gauge className="h-4 w-4 text-blue-500 dark:text-blue-400" /> Deskripsi
                  </dt>
                  <dd className="mt-1 text-gray-600 dark:text-gray-300">
                    {product.description || "Tidak ada deskripsi"}
                  </dd>
                </div>
              </dl>

              {product.status === ProductStatus.AVAILABLE && (
                <div className="mt-8">
                  <Link href={`/user/catalog/${product.id}/rent`}>
                    <SoundButton variant="gradient" size="mobile" className="w-full" soundType="click">
                      Lanjutkan ke Form Penyewaan
                    </SoundButton>
                  </Link>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  } catch (error) {
    console.error("Error fetching product:", error);
    notFound();
  }
}