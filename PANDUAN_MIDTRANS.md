# Panduan Konfigurasi Midtrans

Untuk mengaktifkan integrasi pembayaran Midtrans pada aplikasi Rental Ganset, Anda perlu mengikuti langkah-langkah berikut:

## 1. Buat file .env.local

Buat file bernama `.env.local` di root project dengan isi sebagai berikut:

```
# Database
DATABASE_URL="postgresql://postgres:postgres@localhost:5432/rental_ganset"

# NextAuth
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="rahasia_yang_sangat_rahasia_untuk_nextauth"

# Google OAuth
GOOGLE_CLIENT_ID="GANTI_DENGAN_GOOGLE_CLIENT_ID_ANDA"
GOOGLE_CLIENT_SECRET="GANTI_DENGAN_GOOGLE_CLIENT_SECRET_ANDA"

# Midtrans Configuration
MIDTRANS_SERVER_KEY="SB-Mid-server-GANTI_DENGAN_SERVER_KEY_MIDTRANS"
NEXT_PUBLIC_MIDTRANS_CLIENT_KEY="SB-Mid-client-GANTI_DENGAN_CLIENT_KEY_MIDTRANS"

# App URL
NEXT_PUBLIC_APP_URL="http://localhost:3000"
```

## 2. Dapatkan Key Midtrans

1. Daftar/login ke [Midtrans Dashboard](https://dashboard.midtrans.com/)
2. Untuk pengujian, gunakan environment Sandbox
3. Dapatkan Server Key dan Client Key dari menu Settings > Access Keys
4. Salin kedua key tersebut ke file `.env.local` Anda

## 3. Restart Aplikasi

Setelah menambahkan konfigurasi, restart aplikasi Next.js Anda:

```
npm run dev
```

## Catatan Penting

- **Sandbox Mode**: Key yang dimulai dengan `SB-` adalah untuk mode sandbox (pengujian)
- **Production Mode**: Untuk production, gunakan key yang dimulai dengan `Mid-`
- **Keamanan**: Jangan bagikan Server Key Anda kepada siapapun
- **Performa**: Jika menggunakan vercel atau platform lain, pastikan untuk menambahkan variabel lingkungan di dashboard mereka

## Verifikasi Konfigurasi

Untuk memverifikasi bahwa konfigurasi Midtrans sudah benar, coba lakukan pemesanan baru dan periksa apakah form pembayaran Midtrans muncul.
