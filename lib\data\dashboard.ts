import { DashboardStats } from "../types/dashboard";
import { prisma } from "@/lib/config/prisma";

export async function getStats(): Promise<DashboardStats> {
  try {
    console.log("Fetching real dashboard stats from database...");

    // Get current date and previous month for comparison
    const now = new Date();
    const currentMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const previousMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const currentMonthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);

    // Execute all queries in parallel for better performance
    const [
      totalRevenue,
      previousMonthRevenue,
      totalRentals,
      previousMonthRentals,
      totalProducts,
      previousMonthProducts,
      activeRentals,
      totalProductsAvailable,
      recentRentalsData,
      monthlyRevenueData,
      monthlyRentalsData,
      topProductsData
    ] = await Promise.all([
      // Total revenue from completed payments
      prisma.payment.aggregate({
        _sum: { amount: true },
        where: {
          status: { in: ["DEPOSIT_PAID", "FULLY_PAID"] },
          createdAt: { gte: currentMonth, lte: currentMonthEnd }
        }
      }),

      // Previous month revenue for growth calculation
      prisma.payment.aggregate({
        _sum: { amount: true },
        where: {
          status: { in: ["DEPOSIT_PAID", "FULLY_PAID"] },
          createdAt: { gte: previousMonth, lt: currentMonth }
        }
      }),

      // Total rentals this month
      prisma.rental.count({
        where: {
          createdAt: { gte: currentMonth, lte: currentMonthEnd }
        }
      }),

      // Previous month rentals for growth calculation
      prisma.rental.count({
        where: {
          createdAt: { gte: previousMonth, lt: currentMonth }
        }
      }),

      // Total products this month
      prisma.product.count({
        where: {
          createdAt: { gte: currentMonth, lte: currentMonthEnd }
        }
      }),

      // Previous month products for growth calculation
      prisma.product.count({
        where: {
          createdAt: { gte: previousMonth, lt: currentMonth }
        }
      }),

      // Active rentals for occupancy rate
      prisma.rental.count({
        where: {
          status: { in: ["CONFIRMED", "ACTIVE"] }
        }
      }),

      // Total available products for occupancy rate
      prisma.product.count({
        where: {
          status: "AVAILABLE"
        }
      }),

      // Recent rentals with customer and product info
      prisma.rental.findMany({
        take: 5,
        orderBy: { createdAt: "desc" },
        include: {
          user: { select: { name: true } },
          product: { select: { name: true } }
        }
      }),

      // Monthly revenue for the last 12 months
      prisma.$queryRaw`
        SELECT
          EXTRACT(MONTH FROM p."createdAt") as month,
          EXTRACT(YEAR FROM p."createdAt") as year,
          SUM(p.amount) as revenue
        FROM "Payment" p
        WHERE p.status IN ('DEPOSIT_PAID', 'FULLY_PAID')
          AND p."createdAt" >= ${new Date(now.getFullYear() - 1, now.getMonth(), 1)}
        GROUP BY EXTRACT(YEAR FROM p."createdAt"), EXTRACT(MONTH FROM p."createdAt")
        ORDER BY year, month
      `,

      // Monthly rentals for the last 12 months
      prisma.$queryRaw`
        SELECT
          EXTRACT(MONTH FROM r.created_at) as month,
          EXTRACT(YEAR FROM r.created_at) as year,
          COUNT(*) as count
        FROM rentals r
        WHERE r.created_at >= ${new Date(now.getFullYear() - 1, now.getMonth(), 1)}
        GROUP BY EXTRACT(YEAR FROM r.created_at), EXTRACT(MONTH FROM r.created_at)
        ORDER BY year, month
      `,

      // Top products by rental count
      prisma.$queryRaw`
        SELECT
          p.id,
          p.name,
          p.capacity,
          COUNT(r.id) as rental_count
        FROM "Product" p
        LEFT JOIN rentals r ON p.id = r.product_id
        GROUP BY p.id, p.name, p.capacity
        ORDER BY rental_count DESC
        LIMIT 4
      `
    ]);

    // Calculate growth percentages
    const currentRevenue = totalRevenue._sum.amount || 0;
    const prevRevenue = previousMonthRevenue._sum.amount || 0;
    const revenueGrowth = prevRevenue > 0 ? ((currentRevenue - prevRevenue) / prevRevenue) * 100 : 0;

    const rentalGrowth = previousMonthRentals > 0 ? ((totalRentals - previousMonthRentals) / previousMonthRentals) * 100 : 0;
    const productGrowth = previousMonthProducts > 0 ? ((totalProducts - previousMonthProducts) / previousMonthProducts) * 100 : 0;

    // Calculate occupancy rate
    const occupancyRate = totalProductsAvailable > 0 ? Math.round((activeRentals / totalProductsAvailable) * 100) : 0;

    // Format recent rentals
    const formattedRecentRentals = recentRentalsData.map((rental: any) => ({
      id: rental.id,
      productName: rental.product.name,
      customerName: rental.user.name,
      amount: rental.amount,
      status: rental.status,
      date: rental.createdAt
    }));

    // Format monthly revenue data
    const monthNames = ["Jan", "Feb", "Mar", "Apr", "Mei", "Jun", "Jul", "Agu", "Sep", "Okt", "Nov", "Des"];
    const monthlyRevenue = monthNames.map((monthName, index) => {
      const monthData = (monthlyRevenueData as any[]).find((item: any) =>
        Number(item.month) === index + 1 && Number(item.year) === now.getFullYear()
      );
      return {
        month: monthName,
        revenue: monthData ? Number(monthData.revenue) : 0
      };
    });

    // Format monthly rentals data
    const monthlyRentals = monthNames.map((monthName, index) => {
      const monthData = (monthlyRentalsData as any[]).find((item: any) =>
        Number(item.month) === index + 1 && Number(item.year) === now.getFullYear()
      );
      return {
        month: monthName,
        count: monthData ? Number(monthData.count) : 0
      };
    });

    // Format top products data
    const topProducts = (topProductsData as any[]).map((product: any) => ({
      id: product.id,
      name: product.name,
      capacity: Number(product.capacity),
      rentalCount: Number(product.rental_count)
    }));

    // Get total counts for overall stats
    const [totalRevenueAll, totalRentalsAll, totalProductsAll] = await Promise.all([
      prisma.payment.aggregate({
        _sum: { amount: true },
        where: {
          status: { in: ["DEPOSIT_PAID", "FULLY_PAID"] }
        }
      }),
      prisma.rental.count(),
      prisma.product.count()
    ]);

    const dashboardData = {
      totalRevenue: totalRevenueAll._sum.amount || 0,
      revenueGrowth: Math.round(revenueGrowth * 100) / 100,
      totalRentals: totalRentalsAll,
      rentalGrowth: Math.round(rentalGrowth * 100) / 100,
      totalProducts: totalProductsAll,
      productGrowth: Math.round(productGrowth * 100) / 100,
      occupancyRate,
      occupancyGrowth: 0, // We can implement this later if needed
      recentRentals: formattedRecentRentals,
      monthlyRevenue,
      monthlyRentals,
      topProducts
    };

    console.log("Dashboard stats fetched successfully from database");
    console.log("Stats summary:", {
      totalRevenue: dashboardData.totalRevenue,
      totalRentals: dashboardData.totalRentals,
      totalProducts: dashboardData.totalProducts,
      occupancyRate: dashboardData.occupancyRate
    });

    return dashboardData;

  } catch (error) {
    console.error('Error fetching dashboard stats:', error);

    // Return fallback data in case of database error
    return {
      totalRevenue: 0,
      revenueGrowth: 0,
      totalRentals: 0,
      rentalGrowth: 0,
      totalProducts: 0,
      productGrowth: 0,
      occupancyRate: 0,
      occupancyGrowth: 0,
      recentRentals: [],
      monthlyRevenue: [],
      monthlyRentals: [],
      topProducts: []
    };
  }
}
