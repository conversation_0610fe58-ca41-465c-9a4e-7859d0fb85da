import { NextResponse } from "next/server";

// Dummy endpoint to prevent 404 errors
// This endpoint was removed as part of WhatsApp cleanup
// but some components might still be trying to access it

export async function GET() {
  return NextResponse.json({
    success: false,
    message: "WhatsApp auto-notifications have been simplified. Only order notifications are active.",
    data: {
      notifications: [],
      count: 0,
      hasUnread: false
    }
  }, { status: 410 }); // 410 Gone - indicates the resource is no longer available
}

export async function POST() {
  return NextResponse.json({
    success: false,
    message: "WhatsApp auto-notifications have been simplified. Only order notifications are active.",
    error: "This endpoint has been removed"
  }, { status: 410 }); // 410 Gone
}
