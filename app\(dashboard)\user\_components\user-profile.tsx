'use client';

import { useSession } from 'next-auth/react';
import { signOut } from 'next-auth/react';
import { LuLogOut } from 'react-icons/lu';
import { But<PERSON> } from "@/app/components/ui/button";

export function UserProfile() {
    const { data: session } = useSession();
    const name = session?.user?.name || '';
    const initial = name.charAt(0).toUpperCase();

    return (
        <div className="hidden md:flex items-center gap-4">
            <div className="flex items-center">
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white">
                    {initial}
                </div>
                <span className="ml-2 text-sm font-medium">{name}</span>
            </div>
            <Button
                onClick={() => signOut()}
                variant="destructive"
                size="sm"
                className="flex items-center gap-2"
            >
                <LuLogOut className="w-4 h-4" />
                Ke<PERSON>ar
            </Button>
        </div>
    );
}
