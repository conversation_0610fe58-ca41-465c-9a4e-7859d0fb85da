'use client';

import { useSession } from 'next-auth/react';
import { signOut } from 'next-auth/react';
import { <PERSON><PERSON><PERSON>O<PERSON>, <PERSON><PERSON><PERSON>, LuChevronDown } from 'react-icons/lu';
import { Button } from "@/app/components/ui/button";
import { cn } from "@/lib/utils/cn";
import { useState } from 'react';

export function UserProfile() {
    const { data: session } = useSession();
    const [isDropdownOpen, setIsDropdownOpen] = useState(false);
    const name = session?.user?.name || '';
    const email = session?.user?.email || '';
    const initial = name.charAt(0).toUpperCase();

    return (
        <div className="hidden md:flex items-center gap-3">
            {/* User Info Card */}
            <div className="relative">
                <button
                    onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                    className={cn(
                        "group flex items-center gap-3 min-h-[44px] px-4 py-2 rounded-xl transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98]",
                        "bg-white/80 border border-violet-100 hover:border-violet-200 shadow-sm hover:shadow-md",
                        "dark:bg-gray-800/80 dark:border-violet-800 dark:hover:border-violet-700",
                        "focus:outline-none focus:ring-2 focus:ring-violet-500 focus:ring-offset-2",
                        isDropdownOpen && "bg-violet-50 border-violet-200 dark:bg-violet-900/20 dark:border-violet-700"
                    )}
                    aria-expanded={isDropdownOpen}
                    aria-label="Menu profil pengguna"
                >
                    {/* Avatar */}
                    <div className="relative">
                        <div className={cn(
                            "w-10 h-10 rounded-full flex items-center justify-center text-white font-semibold text-sm shadow-md transition-all duration-200",
                            "bg-gradient-to-br from-violet-600 to-purple-600 group-hover:from-violet-700 group-hover:to-purple-700",
                            "group-hover:scale-105"
                        )}>
                            {initial || <LuUser className="w-5 h-5" />}
                        </div>
                        {/* Online indicator */}
                        <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-emerald-500 border-2 border-white dark:border-gray-800 rounded-full animate-pulse" />
                    </div>

                    {/* User Info */}
                    <div className="flex flex-col items-start min-w-0">
                        <span className="text-sm font-semibold text-violet-900 dark:text-violet-100 truncate max-w-32">
                            {name || 'Pengguna'}
                        </span>
                        {email && (
                            <span className="text-xs text-violet-600 dark:text-violet-400 truncate max-w-32">
                                {email}
                            </span>
                        )}
                    </div>

                    {/* Dropdown Arrow */}
                    <LuChevronDown className={cn(
                        "w-4 h-4 text-violet-600 dark:text-violet-400 transition-transform duration-200",
                        isDropdownOpen && "rotate-180"
                    )} />
                </button>

                {/* Dropdown Menu */}
                {isDropdownOpen && (
                    <>
                        {/* Backdrop */}
                        <div
                            className="fixed inset-0 z-10"
                            onClick={() => setIsDropdownOpen(false)}
                            aria-hidden="true"
                        />

                        {/* Dropdown Content */}
                        <div className="absolute top-full right-0 mt-2 w-64 bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl rounded-xl shadow-2xl border border-violet-100 dark:border-violet-800 z-20 overflow-hidden">
                            {/* User Info Header */}
                            <div className="p-4 bg-gradient-to-r from-violet-50 to-purple-50 dark:from-violet-950/50 dark:to-purple-950/50 border-b border-violet-100 dark:border-violet-800">
                                <div className="flex items-center gap-3">
                                    <div className="w-12 h-12 bg-gradient-to-br from-violet-600 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold shadow-lg">
                                        {initial || <LuUser className="w-6 h-6" />}
                                    </div>
                                    <div className="flex-1 min-w-0">
                                        <h3 className="font-semibold text-violet-900 dark:text-violet-100 truncate">
                                            {name || 'Pengguna'}
                                        </h3>
                                        {email && (
                                            <p className="text-sm text-violet-600 dark:text-violet-400 truncate">
                                                {email}
                                            </p>
                                        )}
                                    </div>
                                </div>
                            </div>

                            {/* Logout Section */}
                            <div className="p-3">
                                <Button
                                    onClick={() => {
                                        setIsDropdownOpen(false);
                                        signOut({ callbackUrl: "/" });
                                    }}
                                    variant="destructive"
                                    size="mobile"
                                    className="w-full min-h-[44px] flex items-center justify-center gap-3 text-white shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98] transition-all duration-200"
                                >
                                    <LuLogOut className="w-5 h-5" />
                                    Keluar dari Akun
                                </Button>
                            </div>
                        </div>
                    </>
                )}
            </div>
        </div>
    );
}
