"use server";

import { prisma } from "@/lib/config/prisma";
import { auth } from "@/auth";
import { revalidatePath } from "next/cache";
import { RentalResponse, RentalResult } from "@/lib/types/rental";
import { getUserStatus } from "./user";
import { calculateDeposit } from "@/lib/utils/calculate";
import { notifyAllAdmins } from "@/lib/notifications";
import MidtransService from "@/lib/services/midtrans";
import { RentalStatus } from "@prisma/client";
import { WhatsAppService } from "@/lib/services/whatsapp";

export async function createRental(formData: FormData): Promise<RentalResponse> {
    try {
        const session = await auth();
        if (!session?.user) {
            return {
                success: false,
                message: "Unauthorized"
            };
        }

        // Ambil dan validasi data dari FormData
        const productId = formData.get('productId');
        const startDate = formData.get('startDate');
        const endDate = formData.get('endDate');
        const duration = formData.get('duration');
        const purpose = formData.get('purpose');
        const arrivalTime = formData.get('arrivalTime');
        const quantity = Number(formData.get('quantity'));
        const amount = Number(formData.get('amount'));

        // Log untuk debugging
        console.log("Received rental data:", {
            productId, startDate, endDate, duration,
            purpose, arrivalTime, quantity, amount
        });

        // Validasi data dasar
        if (!productId || !startDate || !endDate || !duration ||
            !purpose || !arrivalTime || !quantity || !amount) {
            return {
                success: false,
                message: "Data tidak lengkap"
            };
        }

        // Cek ketersediaan produk
        const product = await prisma.product.findUnique({
            where: { id: productId as string }
        });

        if (!product) {
            return {
                success: false,
                message: "Produk tidak ditemukan"
            };
        }

        // Validasi status dan stok
        if (product.stock < quantity) {
            return {
                success: false,
                message: `Stok tidak mencukupi. Tersedia: ${product.stock} unit`
            };
        }

        // Buat rental dengan transaksi
        const result = await prisma.$transaction(async (tx) => {
            // Buat rental
            const rental = await tx.rental.create({
                data: {
                    userId: session.user.id,
                    productId: productId as string,
                    startDate: new Date(startDate as string),
                    endDate: new Date(endDate as string),
                    duration: duration as string,
                    location: "", // Tetap mengisi field location dengan string kosong
                    purpose: purpose as string,
                    status: "PENDING",
                    amount: amount,
                    quantity: quantity,
                    arrivalTime: arrivalTime as string,
                    payment: {
                        create: {
                            amount: amount,
                            status: "DEPOSIT_PENDING",
                            deposit: amount * 0.5, // 50% deposit
                            remaining: amount * 0.5 // 50% remaining balance
                        }
                    }
                },
                include: {
                    payment: true,
                    product: true,
                    user: true
                }
            });

            // Buat token pembayaran Midtrans
            const payment = await MidtransService.createPayment({
                orderId: rental.id,
                amount: amount,
                name: session.user.name || 'Customer',
                email: session.user.email || '<EMAIL>',
                productName: rental.product.name
            });

            // Update rental dengan snapToken
            const updatedRental = await tx.rental.update({
                where: { id: rental.id },
                data: {
                    payment: {
                        update: {
                            snapToken: payment.token
                        }
                    }
                },
                include: {
                    product: {
                        include: {
                            user: true
                        }
                    },
                    user: true,
                    payment: true
                }
            });

            return {
                ...updatedRental,
                snapToken: payment.token
            };
        });

        // Kirim notifikasi ke admin tentang penyewaan baru
        await notifyAllAdmins({
            title: "Penyewaan Baru",
            message: `Penyewaan baru untuk ${product.name} (${quantity} unit) telah dibuat oleh ${session.user.name || session.user.email}`,
            type: "new_rental"
        });

        // Send WhatsApp notification to admin
        try {
            await WhatsAppService.sendAdminOrderNotification(
                result.id,
                session.user.name || session.user.email || 'Customer',
                session.user.phone || 'Not provided',
                session.user.email || 'Not provided',
                product.name,
                `${product.capacity} kVA`,
                new Date(startDate as string),
                new Date(endDate as string),
                '', // location - empty for this flow
                new Date(),
                'New Order'
            );
            console.log(`✅ WhatsApp admin notification sent for new rental: ${result.id}`);
        } catch (whatsappError) {
            console.error('❌ Failed to send WhatsApp admin notification:', whatsappError);
            // Don't fail the rental creation if WhatsApp notification fails
        }

        revalidatePath("/catalog");
        revalidatePath("/user/rentals");

        return {
            success: true,
            message: "Rental berhasil dibuat",
            data: {
                id: result.id,
                snapToken: result.snapToken
            }
        };
    } catch (error) {
        console.error("[CREATE_RENTAL_ERROR]", error);
        return {
            success: false,
            message: error instanceof Error ? error.message : "Gagal membuat rental"
        };
    }
}

export async function updateRentalStatus(
    rentalId: string,
    status: string
): Promise<RentalResult> {
    try {
        const rental = await prisma.rental.update({
            where: { id: rentalId },
            data: {
                status: status as RentalStatus,
                payment: {
                    update: {
                        status: status === "COMPLETED" ? "FULLY_PAID" : "DEPOSIT_PENDING"
                    }
                }
            },
            include: {
                product: true,
                payment: true,
                user: {
                    select: {
                        id: true,
                        name: true,
                        email: true
                    }
                }
            },
        });

        const totalPrice = rental.amount;

        revalidatePath("/user/rentals");
        revalidatePath("/admin/rentals");
        revalidatePath(`/user/rentals/${rentalId}`);
        revalidatePath(`/admin/rentals/${rentalId}`);

        // Calculate duration based on timestamps
        const durationHours = Math.ceil((new Date(rental.endDate).getTime() - new Date(rental.startDate).getTime()) / (1000 * 60 * 60));
        const durationShifts = Math.ceil(durationHours / 8);

        return {
            success: true,
            message: `Rental status updated to ${status}`,
            rental: {
                id: rental.id,
                userId: rental.userId,
                productId: rental.productId,
                startDate: rental.startDate,
                status: rental.status,
                arrivalTime: rental.arrivalTime,
                duration: durationShifts,
                address: rental.address || "",
                notes: rental.notes || undefined,
                totalPrice: totalPrice,
                createdAt: rental.createdAt,
                updatedAt: rental.updatedAt,
                product: {
                    id: rental.product.id,
                    name: rental.product.name,
                    capacity: rental.product.capacity,
                    description: rental.product.description || undefined
                },
                user: {
                    id: rental.user.id,
                    name: rental.user.name || 'User',
                    email: rental.user.email
                },
                operationalStart: rental.operationalStart,
                operationalEnd: rental.operationalEnd
            }
        };
    } catch (error) {
        console.error("Error updating rental status:", error);
        return {
            success: false,
            message: "Failed to update rental status"
        };
    }
}

export async function deleteRental(rentalId: string): Promise<RentalResponse> {
    try {
        // Validate session
        const session = await auth();
        if (!session?.user || session.user.role !== "ADMIN") {
            return {
                success: false,
                message: "Unauthorized"
            };
        }

        // Check if rental exists
        const rental = await prisma.rental.findUnique({
            where: { id: rentalId },
            include: { payment: true }
        });

        if (!rental) {
            return {
                success: false,
                message: "Rental tidak ditemukan"
            };
        }

        // Use a transaction to delete both payment and rental
        await prisma.$transaction(async (tx) => {
            // First delete associated payment if it exists
            if (rental.payment) {
                await tx.payment.delete({
                    where: { id: rental.payment.id }
                });
            }

            // Then delete the rental
            await tx.rental.delete({
                where: { id: rentalId }
            });
        });

        // Revalidate paths to update UI
        revalidatePath("/admin/rentals");
        revalidatePath(`/admin/rentals/${rentalId}`);

        return {
            success: true,
            message: "Rental berhasil dihapus",
            data: {
                id: rentalId,
                redirectTo: "/admin/rentals"
            }
        };
    } catch (error) {
        console.error("Error deleting rental:", error);
        return {
            success: false,
            message: error instanceof Error ? error.message : "Gagal menghapus rental"
        };
    }
}

export async function createUserRental(
    productId: string,
    startDate: string,
    endDate: string,
    location: string,
    deliveryAddress: string,
    notes: string,
    totalAmount: number
): Promise<RentalResponse> {
    try {
        const session = await auth();
        if (!session?.user) {
            return {
                success: false,
                message: "Unauthorized"
            };
        }

        const userId = session.user.id;

        // Cek status user (dikenal atau tidak)
        const { isKnownUser } = await getUserStatus();

        // Hitung deposit berdasarkan status user
        const downPayment = calculateDeposit(totalAmount, isKnownUser);

        // Buat rental record
        const rental = await prisma.rental.create({
            data: {
                userId,
                productId,
                startDate: new Date(startDate),
                endDate: new Date(endDate),
                location: location,
                purpose: "rental", // Default purpose
                arrivalTime: new Date().toISOString(), // Default arrival time
                quantity: 1, // Default quantity
                amount: totalAmount, // Use amount instead of totalAmount
                status: "PENDING", // Status awal
                payment: {
                    create: {
                        amount: totalAmount,
                        deposit: downPayment,
                        remaining: totalAmount - downPayment,
                        status: isKnownUser ? "INVOICE_ISSUED" : "DEPOSIT_PENDING"
                    }
                }
            },
            include: {
                payment: true,
                product: true
            }
        });

        // Send WhatsApp notification to admin for new rental
        try {
            await WhatsAppService.sendAdminOrderNotification(
                rental.id,
                session.user.name || session.user.email || 'Customer',
                session.user.phone || 'Not provided',
                session.user.email || 'Not provided',
                rental.product.name,
                `${rental.product.capacity} kVA`,
                new Date(startDate),
                new Date(endDate),
                location,
                new Date(),
                'New Order'
            );
            console.log(`✅ WhatsApp admin notification sent for new user rental: ${rental.id}`);
        } catch (whatsappError) {
            console.error('❌ Failed to send WhatsApp admin notification:', whatsappError);
            // Don't fail the rental creation if WhatsApp notification fails
        }

        // Jika user tidak dikenal, buat token pembayaran untuk deposit
        if (!isKnownUser && rental.payment) {
            const payment = await MidtransService.createPayment({
                orderId: rental.id,
                amount: downPayment,
                name: session.user.name || "Customer",
                email: session.user.email || "<EMAIL>",
                productName: `Deposit untuk sewa produk ID: ${productId}`
            });

            // Update payment dengan token Midtrans
            await prisma.payment.update({
                where: { id: rental.payment.id },
                data: { snapToken: payment.token }
            });

            return {
                success: true,
                message: "Rental created, waiting for deposit payment",
                data: {
                    id: rental.id,
                    snapToken: payment.token
                }
            };
        } else {
            // Untuk user dikenal, langsung buat invoice
            return {
                success: true,
                message: "Rental created with invoice",
                data: {
                    id: rental.id,
                    snapToken: "" // Empty string instead of isInvoice flag
                }
            };
        }
    } catch (error) {
        console.error("Error creating rental:", error);
        return {
            success: false,
            message: error instanceof Error ? error.message : "Failed to create rental"
        };
    }
}
