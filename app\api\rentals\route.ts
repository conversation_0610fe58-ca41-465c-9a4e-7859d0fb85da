import { NextResponse } from "next/server";
import { prisma } from "@/lib/config/prisma";
import { auth } from "@/auth";
import { revalidatePath } from "next/cache";
import { RentalStatus, PaymentStatus } from "@prisma/client";
import { addressSchema } from "@/lib/validations/rental/schema";
import { NotificationService } from "@/lib/services/notification-service";
import { WhatsAppService } from "@/lib/services/whatsapp";

export async function POST(request: Request) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const data = await request.json();
    const {
      productId,
      startDate,
      endDate,
      arrivalTime,
      deliveryType,
      deliveryAddress,
      notes,
      purpose,
      quantity,
      totalAmount,
      downPayment
    } = data;

    // Validasi input
    if (!productId || !startDate || !endDate) {
      return NextResponse.json(
        { error: "Data tidak lengkap" },
        { status: 400 }
      );
    }

    // Validasi alamat dengan skema Zod
    let validatedAddress = deliveryAddress;
    try {
      const result = addressSchema.safeParse(deliveryAddress);
      if (!result.success) {
        // Tampilkan pesan error yang lebih spesifik dari Zod
        const errorMessage = result.error.issues[0]?.message || "Alamat pengiriman tidak valid (minimal 10 karakter)";
        return NextResponse.json(
          { error: errorMessage },
          { status: 400 }
        );
      }
      validatedAddress = result.data; // Simpan alamat yang sudah divalidasi
    } catch (error) {
      console.error("Error validating address:", error);
      return NextResponse.json(
        { error: "Terjadi kesalahan saat validasi alamat" },
        { status: 400 }
      );
    }

    // Buat rental baru
    const rental = await prisma.rental.create({
      data: {
        userId: session.user.id,
        productId,
        startDate: new Date(startDate),
        endDate: new Date(endDate),
        arrivalTime,
        duration: deliveryType,
        address: validatedAddress, // Gunakan alamat yang sudah divalidasi
        notes,
        quantity: quantity || 1,
        amount: totalAmount,
        status: "PENDING" as RentalStatus,
        location: "on_site",
        purpose: purpose || "rental",
        payment: {
          create: {
            userId: session.user.id,
            amount: totalAmount,
            deposit: downPayment,
            remaining: totalAmount - downPayment,
            status: "DEPOSIT_PENDING" as PaymentStatus,
            transactionId: `INV-${Date.now()}`
          }
        }
      },
      include: {
        product: true,
        user: true
      }
    });

    // Ambil data rental dengan payment
    const rentalWithPayment = await prisma.rental.findUnique({
      where: { id: rental.id },
      include: { payment: true }
    });

    // Buat notifikasi untuk admin tentang rental baru
    try {
      const notificationService = new NotificationService();

      // Cari admin pertama (bisa diperbaiki untuk mendukung multiple admin)
      const admin = await prisma.user.findFirst({
        where: { role: "ADMIN" }
      });

      if (admin) {
        await notificationService.createNewRentalNotification(
          session.user.id,
          admin.id,
          rental.id
        );

        // Buat notifikasi untuk payment baru juga
        if (rentalWithPayment?.payment) {
          await notificationService.createNewPaymentNotification(
            admin.id,
            rentalWithPayment.payment.id,
            rental.id
          );
        }
      }
    } catch (notificationError) {
      console.error("Error creating notification:", notificationError);
      // Jangan gagalkan request jika notifikasi gagal
    }

    // Send WhatsApp notification to admin for new rental
    try {
      await WhatsAppService.sendAdminOrderNotification(
        rental.id,
        rental.user.name || rental.user.email || 'Customer',
        rental.user.phone || 'Not provided',
        rental.user.email || 'Not provided',
        rental.product.name,
        `${rental.product.capacity} kVA`,
        new Date(startDate),
        new Date(endDate),
        validatedAddress || 'To be confirmed',
        new Date(),
        'New Order'
      );
      console.log(`✅ WhatsApp admin notification sent for new rental API: ${rental.id}`);
    } catch (whatsappError) {
      console.error('❌ Failed to send WhatsApp admin notification:', whatsappError);
      // Don't fail the rental creation if WhatsApp notification fails
    }

    // Revalidasi path terkait
    revalidatePath("/user/rentals");
    revalidatePath("/admin/rentals");
    revalidatePath("/admin/payments");

    return NextResponse.json({
      id: rental.id,
      payment: rentalWithPayment?.payment
    });
  } catch (error) {
    console.error("Error creating rental:", error);
    return NextResponse.json(
      { error: "Gagal membuat pesanan" },
      { status: 500 }
    );
  }
}
