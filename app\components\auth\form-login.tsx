"use client";
import { useRouter } from "next/navigation";
import { useState } from "react";
import Link from "next/link";
import { signIn } from "next-auth/react";
import { SignInSchema } from "@/lib/validations/user/schema";
import { Mail, Lock, Eye, EyeOff, AlertCircle, CheckCircle } from "lucide-react";
import { cn } from "@/lib/utils";

export default function FromLogin() {
    const router = useRouter();
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState("");
    const [success, setSuccess] = useState("");
    const [formErrors, setFormErrors] = useState<{ email?: string, password?: string }>({});
    const [rememberMe, setRememberMe] = useState(false);
    const [showPassword, setShowPassword] = useState(false);

    const validateForm = (email: string, password: string) => {
        try {
            // Validasi dengan Zod
            SignInSchema.parse({ email, password });
            setFormErrors({});
            return true;
        } catch (error: unknown) {
            // Extract validation errors
            const fieldErrors: { email?: string, password?: string } = {};
            const zodError = error as { errors?: Array<{ path: string[], message: string }> };

            if (zodError.errors) {
                zodError.errors.forEach((err) => {
                    const path = err.path[0]; // 'email' atau 'password'
                    if (path === 'email' || path === 'password') {
                        fieldErrors[path] = err.message;
                    }
                });
            }
            setFormErrors(fieldErrors);
            return false;
        }
    };

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        setIsLoading(true);
        setError("");  // Reset error setiap kali submit
        setSuccess("");

        try {
            // Ambil nilai input
            const email = (e.currentTarget.elements.namedItem("email") as HTMLInputElement).value;
            const password = (e.currentTarget.elements.namedItem("password") as HTMLInputElement).value;

            // Validasi dengan Zod
            if (!validateForm(email, password)) {
                setIsLoading(false);
                return;
            }

            // Simulasi loading untuk efek UI yang lebih baik
            setSuccess("Memeriksa kredensial...");

            const result = await signIn("credentials", {
                email,
                password,
                redirect: false,
            });

            if (result?.error) {
                setSuccess("");
                if (result.error === 'CredentialsSignin') {
                    setError("Email atau password salah");
                } else {
                    setError("Terjadi kesalahan saat login. Silakan coba lagi.");
                }
                return;
            }

            // Set success message sebelum redirect
            setSuccess("Login berhasil! Mengalihkan...");

            // Delay sedikit untuk menampilkan pesan sukses
            setTimeout(() => {
                try {
                    // Gunakan router.replace untuk menghindari fetch error
                    router.replace('/');
                } catch (error) {
                    // Fallback ke window.location jika router gagal
                    if (typeof window !== 'undefined') {
                        window.location.href = '/';
                    }
                }
            }, 800);
        } catch (error) {
            console.error("Login error:", error);
            setSuccess("");

            // Handle different types of errors
            if (error instanceof TypeError && error.message.includes('fetch')) {
                setError("Koneksi bermasalah. Periksa koneksi internet Anda.");
            } else {
                setError("Terjadi kesalahan saat login. Silakan coba lagi.");
            }
        } finally {
            if (!success) {
                setIsLoading(false);
            }
        }
    };

    return (
        <>
            <div className="text-center mb-8">
                <div className="flex justify-center">
                    <div className="h-14 w-14 rounded-full bg-violet-600 flex items-center justify-center text-white mb-4">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <path strokeLinecap="round" strokeLinejoin="round" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                        </svg>
                    </div>
                </div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-1">Masuk ke Akun Anda</h2>
                <p className="text-gray-500 dark:text-gray-400 text-sm">Silakan masuk untuk melanjutkan</p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-5">
                <div className="space-y-4">
                    <div>
                        <label htmlFor="email" className="block text-sm font-medium text-foreground mb-1.5 flex items-center gap-1.5">
                            <Mail className="h-4 w-4 text-primary" />
                            <span>Email</span>
                        </label>
                        <div className="relative rounded-md">
                            <input
                                id="email"
                                name="email"
                                type="email"
                                autoComplete="email"
                                required
                                placeholder="Masukkan email Anda"
                                className={cn(
                                    "block w-full px-4 py-3 border border-input rounded-lg bg-background",
                                    "text-foreground placeholder-muted-foreground",
                                    "focus:outline-none focus:ring-2 focus:ring-ring focus:border-primary",
                                    "transition-all duration-200 shadow-sm",
                                    formErrors.email
                                        ? "border-destructive bg-destructive/5"
                                        : "hover:border-primary/50"
                                )}
                            />
                            {formErrors.email && (
                                <div className="absolute right-2 top-3 text-red-500 dark:text-red-400">
                                    <AlertCircle className="h-5 w-5" />
                                </div>
                            )}
                        </div>
                        {formErrors.email && (
                            <p className="mt-1.5 text-sm text-red-500 dark:text-red-400 flex items-center gap-1">
                                <span className="h-1 w-1 rounded-full bg-red-500 dark:bg-red-400"></span>
                                {formErrors.email}
                            </p>
                        )}
                    </div>

                    <div>
                        <label htmlFor="password" className="block text-sm font-medium text-foreground mb-1.5 flex items-center gap-1.5">
                            <Lock className="h-4 w-4 text-primary" />
                            <span>Password</span>
                        </label>
                        <div className="relative rounded-md">
                            <input
                                id="password"
                                name="password"
                                type={showPassword ? "text" : "password"}
                                autoComplete="current-password"
                                required
                                placeholder="Masukkan password Anda"
                                className={cn(
                                    "block w-full px-4 py-3 border border-input rounded-lg bg-background",
                                    "text-foreground placeholder-muted-foreground",
                                    "focus:outline-none focus:ring-2 focus:ring-ring focus:border-primary",
                                    "transition-all duration-200 shadow-sm pr-10",
                                    formErrors.password
                                        ? "border-destructive bg-destructive/5"
                                        : "hover:border-primary/50"
                                )}
                            />
                            <button
                                type="button"
                                onClick={() => setShowPassword(!showPassword)}
                                className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-violet-600 dark:text-gray-500 dark:hover:text-violet-400 transition-colors"
                            >
                                {showPassword ?
                                    <EyeOff className="h-5 w-5" /> :
                                    <Eye className="h-5 w-5" />
                                }
                            </button>
                        </div>
                        {formErrors.password && (
                            <p className="mt-1.5 text-sm text-red-500 dark:text-red-400 flex items-center gap-1">
                                <span className="h-1 w-1 rounded-full bg-red-500 dark:bg-red-400"></span>
                                {formErrors.password}
                            </p>
                        )}
                    </div>

                    <div className="flex items-center justify-between pt-1">
                        <div className="flex items-center">
                            <div className="relative h-4 w-4 flex items-center justify-center">
                                <input
                                    id="remember-me"
                                    name="remember-me"
                                    type="checkbox"
                                    checked={rememberMe}
                                    onChange={(e) => setRememberMe(e.target.checked)}
                                    className="h-4 w-4 text-red-600 dark:text-red-500 focus:ring-red-500/30 dark:focus:ring-red-400/20 border-gray-300 dark:border-gray-600 rounded transition-colors duration-200 cursor-pointer"
                                />
                            </div>
                            <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-600 dark:text-gray-400 cursor-pointer">
                                Ingat saya
                            </label>
                        </div>
                        <Link href="/forgot-password" className="text-sm font-medium text-violet-600 dark:text-violet-400 hover:text-indigo-500 dark:hover:text-indigo-300 transition-colors">
                            Lupa password?
                        </Link>
                    </div>
                </div>

                {error && (
                    <div className="rounded-lg bg-red-50 dark:bg-red-900/20 p-4 border border-red-200 dark:border-red-800/50 shadow-sm">
                        <div className="flex items-start">
                            <div className="flex-shrink-0 mt-0.5">
                                <AlertCircle className="h-5 w-5 text-red-500 dark:text-red-400" />
                            </div>
                            <div className="ml-3">
                                <p className="text-sm font-medium text-red-700 dark:text-red-300">{error}</p>
                            </div>
                        </div>
                    </div>
                )}

                {success && (
                    <div className="rounded-lg bg-green-50 dark:bg-green-900/20 p-4 border border-green-200 dark:border-green-800/50 shadow-sm">
                        <div className="flex items-start">
                            <div className="flex-shrink-0 mt-0.5">
                                <CheckCircle className="h-5 w-5 text-green-500 dark:text-green-400" />
                            </div>
                            <div className="ml-3">
                                <p className="text-sm font-medium text-green-700 dark:text-green-300">{success}</p>
                            </div>
                        </div>
                    </div>
                )}

                <button
                    type="submit"
                    disabled={isLoading}
                    className="w-full rounded-lg bg-gradient-to-r from-violet-600 to-indigo-600 px-4 py-3.5 text-sm font-medium text-white shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-violet-500 focus:ring-offset-2 disabled:opacity-50 transition-all duration-200 transform hover:-translate-y-0.5"
                >
                    {isLoading ? (
                        <div className="flex items-center justify-center">
                            <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Memproses...
                        </div>
                    ) : (
                        "Masuk"
                    )}
                </button>

                <div className="relative my-2">
                    <div className="absolute inset-0 flex items-center">
                        <div className="w-full border-t border-gray-200 dark:border-gray-700"></div>
                    </div>
                    <div className="relative flex justify-center text-xs">
                        <span className="px-2 bg-white dark:bg-gray-900 text-gray-500 dark:text-gray-400">atau</span>
                    </div>
                </div>

                <p className="text-center text-sm text-gray-600 dark:text-gray-400">
                    Belum punya akun?{" "}
                    <Link href="/register" className="text-violet-600 dark:text-violet-400 hover:text-indigo-500 dark:hover:text-indigo-300 font-medium transition-colors">
                        Daftar sekarang
                    </Link>
                </p>
            </form>
        </>
    );
}
