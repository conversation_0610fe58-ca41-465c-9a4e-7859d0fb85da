'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { LuMenu, LuX, LuLogOut } from 'react-icons/lu';
import { signOut } from 'next-auth/react';
import {
    LuLayoutDashboard as Lu<PERSON><PERSON>,
    LuBox,
    LuClock,
    LuUser
} from 'react-icons/lu';

const menuItems = [
    {
        href: '/user/dashboard',
        title: '<PERSON><PERSON><PERSON>',
        icon: LuH<PERSON>
    },
    {
        href: '/user/catalog',
        title: 'Katalog',
        icon: Lu<PERSON><PERSON>
    },
    {
        href: '/user/rentals',
        title: 'Rental Saya',
        icon: <PERSON><PERSON><PERSON>
    },
    {
        href: '/user/profile',
        title: 'Profil',
        icon: LuUser
    }
];

export function MobileMenu() {
    const [isOpen, setIsOpen] = useState(false);
    const pathname = usePathname();

    return (
        <div className="md:hidden">
            <button
                onClick={() => setIsOpen(!isOpen)}
                className="p-2 text-gray-600"
            >
                {isOpen ? <LuX size={24} /> : <LuMenu size={24} />}
            </button>

            {isOpen && (
                <div className="absolute top-16 left-0 right-0 bg-white shadow-lg">
                    <div className="px-4 py-2 space-y-2">
                        {menuItems.map((item) => {
                            const Icon = item.icon;
                            return (
                                <Link
                                    key={item.href}
                                    href={item.href}
                                    className={`flex items-center px-3 py-2 rounded-md text-sm font-medium ${pathname === item.href
                                        ? 'bg-blue-50 text-blue-600'
                                        : 'text-gray-600 hover:bg-gray-50'
                                        }`}
                                    onClick={() => setIsOpen(false)}
                                >
                                    <Icon className="w-4 h-4 mr-2" />
                                    {item.title}
                                </Link>
                            );
                        })}
                        <button
                            onClick={() => signOut()}
                            className="flex w-full items-center px-3 py-2 text-sm font-medium text-red-600 hover:bg-red-50 rounded-md"
                        >
                            <LuLogOut className="w-4 h-4 mr-2" />
                            Keluar
                        </button>
                    </div>
                </div>
            )}
        </div>
    );
} 
