'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { LuMenu, LuX, LuLogOut } from 'react-icons/lu';
import { signOut } from 'next-auth/react';
import {
    LuLayoutDashboard as LuHome,
    LuBox,
    LuClock,
    LuUser
} from 'react-icons/lu';
import { Button } from "@/app/components/ui/button";
import { cn } from "@/lib/utils/cn";

const menuItems = [
    {
        href: '/user/dashboard',
        title: 'Beranda',
        icon: LuHome
    },
    {
        href: '/user/catalog',
        title: 'Katalog',
        icon: LuBox
    },
    {
        href: '/user/rentals',
        title: 'Ren<PERSON> Saya',
        icon: Lu<PERSON><PERSON>
    },
    {
        href: '/user/profile',
        title: 'Profil',
        icon: LuUser
    }
];

export function MobileMenu() {
    const [isOpen, setIsOpen] = useState(false);
    const [isAnimating, setIsAnimating] = useState(false);
    const pathname = usePathname();

    // Handle menu toggle with animation
    const toggleMenu = () => {
        if (isOpen) {
            setIsAnimating(true);
            setTimeout(() => {
                setIsOpen(false);
                setIsAnimating(false);
            }, 200);
        } else {
            setIsOpen(true);
        }
    };

    // Close menu on route change
    useEffect(() => {
        if (isOpen) {
            toggleMenu();
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [pathname]);

    // Prevent body scroll when menu is open and handle escape key
    useEffect(() => {
        if (isOpen) {
            document.body.style.overflow = 'hidden';

            // Handle escape key
            const handleEscape = (event: KeyboardEvent) => {
                if (event.key === 'Escape') {
                    toggleMenu();
                }
            };

            document.addEventListener('keydown', handleEscape);

            return () => {
                document.removeEventListener('keydown', handleEscape);
                document.body.style.overflow = 'unset';
            };
        } else {
            document.body.style.overflow = 'unset';
        }

        return () => {
            document.body.style.overflow = 'unset';
        };
    }, [isOpen, toggleMenu]);

    return (
        <div className="md:hidden">
            {/* Menu Toggle Button */}
            <Button
                onClick={toggleMenu}
                variant="ghost"
                size="mobile"
                className="relative z-50 min-h-[44px] min-w-[44px] p-2 text-violet-700 hover:text-violet-800 hover:bg-violet-50 dark:text-violet-300 dark:hover:text-violet-200 dark:hover:bg-violet-900/20 transition-all duration-200"
                aria-label={isOpen ? "Tutup menu" : "Buka menu"}
                aria-expanded={isOpen}
            >
                <div className="relative w-6 h-6">
                    <LuMenu
                        className={cn(
                            "absolute inset-0 w-6 h-6 transition-all duration-200",
                            isOpen ? "opacity-0 rotate-90 scale-75" : "opacity-100 rotate-0 scale-100"
                        )}
                    />
                    <LuX
                        className={cn(
                            "absolute inset-0 w-6 h-6 transition-all duration-200",
                            isOpen ? "opacity-100 rotate-0 scale-100" : "opacity-0 -rotate-90 scale-75"
                        )}
                    />
                </div>
            </Button>

            {/* Backdrop */}
            {isOpen && (
                <div
                    className={cn(
                        "fixed inset-0 bg-black/20 backdrop-blur-sm z-40 transition-opacity duration-200",
                        isAnimating ? "opacity-0" : "opacity-100"
                    )}
                    onClick={toggleMenu}
                    onTouchEnd={toggleMenu} // For better mobile touch support
                    aria-hidden="true"
                />
            )}

            {/* Mobile Menu Panel */}
            {isOpen && (
                <div
                    className={cn(
                        "fixed top-0 right-0 h-full w-80 max-w-[85vw] bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl shadow-2xl z-40 transition-all duration-300 ease-out border-l border-violet-100 dark:border-violet-800",
                        isAnimating ? "translate-x-full opacity-0" : "translate-x-0 opacity-100"
                    )}
                    role="dialog"
                    aria-modal="true"
                    aria-label="Menu navigasi"
                >
                    {/* Menu Header */}
                    <div className="p-6 border-b border-violet-100 dark:border-violet-800 bg-gradient-to-r from-violet-50 to-purple-50 dark:from-violet-950/50 dark:to-purple-950/50">
                        <h2 className="text-lg font-semibold text-violet-900 dark:text-violet-100">
                            Menu Navigasi
                        </h2>
                        <p className="text-sm text-violet-600 dark:text-violet-400 mt-1">
                            Pilih halaman yang ingin dikunjungi
                        </p>
                    </div>

                    {/* Menu Items */}
                    <nav className="flex-1 p-4 space-y-2" role="navigation">
                        {menuItems.map((item, index) => {
                            const Icon = item.icon;
                            const isActive = pathname === item.href;

                            return (
                                <Link
                                    key={item.href}
                                    href={item.href}
                                    onClick={toggleMenu}
                                    className={cn(
                                        "group flex items-center min-h-[44px] px-4 py-3 rounded-xl text-sm font-medium transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98]",
                                        "focus:outline-none focus:ring-2 focus:ring-violet-500 focus:ring-offset-2",
                                        isActive
                                            ? "bg-gradient-to-r from-violet-600 to-purple-600 text-white shadow-lg"
                                            : "text-violet-700 hover:bg-violet-50 hover:text-violet-800 dark:text-violet-300 dark:hover:bg-violet-900/20 dark:hover:text-violet-200"
                                    )}
                                    style={{
                                        animationDelay: `${index * 50}ms`,
                                        animation: isOpen ? 'slideInRight 0.3s ease-out forwards' : 'none'
                                    }}
                                >
                                    <Icon className={cn(
                                        "w-5 h-5 mr-3 transition-transform duration-200",
                                        "group-hover:scale-110",
                                        isActive ? "text-white" : "text-violet-600 dark:text-violet-400"
                                    )} />
                                    <span className="flex-1">{item.title}</span>
                                    {isActive && (
                                        <div className="w-2 h-2 bg-white rounded-full ml-2 animate-pulse" />
                                    )}
                                </Link>
                            );
                        })}
                    </nav>

                    {/* Logout Section */}
                    <div className="p-4 border-t border-violet-100 dark:border-violet-800 bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-950/30 dark:to-pink-950/30">
                        <Button
                            onClick={() => signOut({ callbackUrl: "/" })}
                            variant="destructive"
                            size="mobile"
                            className="w-full min-h-[44px] flex items-center justify-center gap-3 text-white shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98] transition-all duration-200"
                        >
                            <LuLogOut className="w-5 h-5" />
                            Keluar dari Akun
                        </Button>
                    </div>
                </div>
            )}

            {/* Animation Keyframes */}
            <style jsx>{`
                @keyframes slideInRight {
                    from {
                        opacity: 0;
                        transform: translateX(20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateX(0);
                    }
                }
            `}</style>
        </div>
    );
}
