import { notFound } from "next/navigation";
import { getRentalById } from "@/lib/data/rental";
import { RentalDetail } from "@/app/components/rental/rental-detail";
import { OperationControl } from "@/app/components/operation/operation-control";
import { auth } from "@/auth";
import { BackButton } from "@/app/components/ui/back-button";
import { Rental } from "@/lib/types/rental";
import { OperationRental } from "@/app/components/operation/operation-control";
import { Alert } from "@/app/components/ui/alert";
import { LuTriangleAlert } from "react-icons/lu";

interface PageProps {
  params: Promise<{
    id: string;
  }>;
  searchParams?: Promise<{
    payment_error?: string;
  }>;
}

// Interface yang memperluas Rental dengan properti tambahan dari API
interface RentalWithExtras extends Rental {
  endDate?: Date | string;
  amount?: number;
  payment?: {
    deposit: number;
    remaining: number;
    overtime: number | null;
    status: string;
  };
}

export default async function Page({ params, searchParams }: PageProps) {
  const { id } = await params;
  const session = await auth();
  const rental = await getRentalById(id) as RentalWithExtras;
  const isAdmin = session?.user?.role === "ADMIN";
  const resolvedSearchParams = searchParams ? await searchParams : {};
  const hasPaymentError = resolvedSearchParams?.payment_error === 'true';

  if (!rental) {
    notFound();
  }

  // Konversi rental ke format yang diterima oleh OperationControl
  const rentalForOperation: OperationRental = {
    id: rental.id,
    startDate: rental.startDate,
    status: rental.status,
    duration: rental.duration || "",
    operationalStart: rental.operationalStart ? new Date(rental.operationalStart) : null,
    operationalEnd: rental.operationalEnd ? new Date(rental.operationalEnd) : null,
    payment: rental.payment ? {
      deposit: rental.payment.deposit || 0,
      remaining: rental.payment.remaining || 0,
      overtime: rental.payment.overtime || null,
      status: rental.payment.status
    } : null
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <BackButton href="/user/rentals" />

      {hasPaymentError && (
        <Alert variant="destructive" title="Pembayaran Gagal">
          <div className="flex items-center gap-2">
            <LuTriangleAlert className="h-4 w-4" />
            <span>
              Pembayaran Anda gagal diproses. Silakan coba lagi atau hubungi customer service jika masalah berlanjut.
            </span>
          </div>
        </Alert>
      )}

      <RentalDetail rental={rental} />
      <OperationControl rental={rentalForOperation as OperationRental} isAdmin={isAdmin} />
    </div>
  );
}
