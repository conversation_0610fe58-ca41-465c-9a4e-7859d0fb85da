"use client";

import { Skeleton } from "@/app/components/ui/skeleton";
import { cn } from "@/lib/utils";

export default function UserLayoutLoading() {
  return (
    <div className="flex min-screen overflow-hidden user-dashboard-container">
      {/* Desktop Sidebar Skeleton */}
      <aside className="hidden lg:flex lg:w-80 lg:flex-col lg:fixed lg:inset-y-0 z-50">
        <div className="flex flex-col flex-1 min-h-0 bg-white/95 backdrop-blur-xl border-r border-violet-200/60 shadow-xl dark:bg-slate-900/95 dark:border-violet-700/60">
          {/* Header Skeleton */}
          <div className="flex items-center h-20 px-6 border-b border-slate-200/60 bg-gradient-to-r from-violet-600 via-purple-600 to-blue-600 dark:from-violet-800 dark:via-purple-800 dark:to-blue-800 dark:border-slate-700/60 shadow-lg">
            <div className="flex items-center gap-3">
              <Skeleton className="w-12 h-12 rounded-2xl bg-white/25" />
              <div className="flex flex-col gap-1">
                <Skeleton className="h-5 w-32 bg-white/25" />
                <Skeleton className="h-3 w-28 bg-white/20" />
              </div>
            </div>
          </div>

          {/* Search Bar Skeleton */}
          <div className="px-4 py-6">
            <Skeleton className="h-12 w-full rounded-xl bg-violet-100/80 dark:bg-violet-900/30" />
          </div>

          {/* Navigation Menu Skeleton */}
          <div className="px-4 flex-1">
            <div className="mb-6">
              <Skeleton className="h-4 w-20 mb-4 bg-violet-100/60 dark:bg-violet-900/20" />
              <nav className="space-y-2">
                {/* Navigation Items with Realistic Widths */}
                {[
                  { width: "w-20", name: "Dashboard" },
                  { width: "w-16", name: "Katalog" },
                  { width: "w-24", name: "Rental Saya" }, // Terpanjang!
                  { width: "w-28", name: "Status Operasi" },
                  { width: "w-20", name: "Pembayaran" },
                  { width: "w-16", name: "Profil" }
                ].map((item, index) => (
                  <div
                    key={index}
                    className="group flex items-center px-3 py-3 rounded-lg"
                    style={{
                      animationDelay: `${index * 100}ms`
                    }}
                  >
                    {/* Icon Skeleton */}
                    <div className="mr-3 rounded-md p-2 bg-gray-100 dark:bg-gray-700">
                      <Skeleton className="h-5 w-5 bg-gray-200 dark:bg-gray-600" />
                    </div>
                    
                    {/* Text Content Skeleton */}
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-1">
                        <Skeleton className={cn("h-4", item.width, "bg-gray-200 dark:bg-gray-600")} />
                        {index === 1 && ( // Katalog has badge
                          <Skeleton className="h-4 w-12 rounded-full bg-violet-100 dark:bg-violet-900/30" />
                        )}
                      </div>
                      <Skeleton className="h-3 w-32 bg-gray-100 dark:bg-gray-700" />
                    </div>
                  </div>
                ))}
              </nav>
            </div>
          </div>

          {/* User Profile Skeleton */}
          <div className="border-t dark:border-gray-700 pt-4 mt-4 px-4">
            <div className="bg-gray-50 dark:bg-gray-800 rounded-xl p-4">
              <div className="flex items-center gap-3 mb-3">
                <Skeleton className="w-10 h-10 rounded-full bg-violet-100 dark:bg-violet-900/30" />
                <div className="flex-1">
                  <Skeleton className="h-4 w-24 mb-1 bg-gray-200 dark:bg-gray-600" />
                  <Skeleton className="h-3 w-32 bg-gray-100 dark:bg-gray-700" />
                </div>
              </div>
              <div className="flex items-center justify-between mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
                <Skeleton className="h-8 w-8 rounded-full bg-gray-200 dark:bg-gray-600" />
                <div className="flex gap-1">
                  <Skeleton className="h-8 w-8 rounded-full bg-gray-200 dark:bg-gray-600" />
                  <Skeleton className="h-8 w-8 rounded-full bg-gray-200 dark:bg-gray-600" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </aside>

      {/* Main Content Area */}
      <div className="flex flex-col flex-1 lg:pl-80">
        {/* Header Skeleton */}
        <header className="sticky top-0 z-40 bg-white/98 backdrop-blur-xl border-b border-violet-200/60 dark:bg-slate-900/98 dark:border-violet-700/60 shadow-lg">
          <div className="flex items-center justify-between h-16 px-4 sm:px-6 lg:px-8">
            <div className="flex items-center gap-4">
              {/* Mobile Menu Button Skeleton */}
              <Skeleton className="lg:hidden h-10 w-10 rounded-xl bg-violet-100 dark:bg-violet-800/50" />
              
              {/* Title Skeleton */}
              <div className="flex flex-col gap-1">
                <Skeleton className="h-5 w-28 bg-gray-200 dark:bg-gray-600" />
                <Skeleton className="h-3 w-20 bg-violet-100 dark:bg-violet-900/30" />
              </div>
            </div>

            {/* Header Actions Skeleton */}
            <div className="flex items-center gap-3">
              {/* Desktop Actions */}
              <div className="hidden lg:flex items-center gap-2">
                <Skeleton className="h-10 w-10 rounded-full bg-gray-200 dark:bg-gray-600" />
                <Skeleton className="h-10 w-10 rounded-full bg-gray-200 dark:bg-gray-600" />
              </div>

              {/* Mobile User Info Skeleton */}
              <div className="lg:hidden flex items-center gap-2">
                <Skeleton className="w-8 h-8 rounded-xl bg-violet-100 dark:bg-violet-900/30" />
                <div className="flex items-center gap-1">
                  <Skeleton className="h-8 w-8 rounded-full bg-gray-200 dark:bg-gray-600" />
                  <Skeleton className="h-8 w-8 rounded-full bg-gray-200 dark:bg-gray-600" />
                </div>
              </div>
            </div>
          </div>
        </header>

        {/* Main Content Skeleton */}
        <main className="flex-1 overflow-y-auto pb-24 md:pb-10">
          <div className="px-4 sm:px-6 lg:px-8 py-6 max-w-7xl mx-auto">
            {/* Page Content Skeleton */}
            <div className="space-y-6">
              {/* Header Section */}
              <div className="bg-gradient-to-r from-violet-50 to-indigo-50 dark:from-violet-950/40 dark:to-indigo-950/40 rounded-xl p-6 shadow-sm border border-gray-100 dark:border-gray-800">
                <Skeleton className="h-8 w-64 mb-3 bg-violet-100/80 dark:bg-violet-900/30" />
                <Skeleton className="h-4 w-96 mb-2 bg-violet-50/80 dark:bg-violet-950/20" />
                <Skeleton className="h-4 w-80 bg-violet-50/80 dark:bg-violet-950/20" />
              </div>

              {/* Content Cards */}
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {Array.from({ length: 3 }).map((_, i) => (
                  <div key={i} className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6">
                    <div className="flex items-center justify-between mb-4">
                      <Skeleton className="h-12 w-12 rounded-full bg-gray-200 dark:bg-gray-600" />
                      <Skeleton className="h-6 w-16 rounded-full bg-gray-100 dark:bg-gray-700" />
                    </div>
                    <Skeleton className="h-6 w-24 mb-2 bg-gray-200 dark:bg-gray-600" />
                    <Skeleton className="h-4 w-32 bg-gray-100 dark:bg-gray-700" />
                  </div>
                ))}
              </div>
            </div>
          </div>
        </main>

        {/* Mobile Bottom Navigation Skeleton */}
        <div className="lg:hidden fixed bottom-0 left-0 right-0 bg-white/95 backdrop-blur-xl border-t border-violet-200/60 dark:bg-slate-900/95 dark:border-violet-700/60 shadow-lg z-40">
          <div className="grid grid-cols-3 h-16">
            {[
              { width: "w-12", name: "Beranda" },
              { width: "w-14", name: "Katalog" },
              { width: "w-12", name: "Rental" }
            ].map((item, index) => (
              <div key={index} className="flex flex-col items-center justify-center px-1 py-2">
                <Skeleton className="w-5 h-5 mb-1 rounded bg-gray-200 dark:bg-gray-600" />
                <Skeleton className={cn("h-3", item.width, "bg-gray-100 dark:bg-gray-700")} />
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
